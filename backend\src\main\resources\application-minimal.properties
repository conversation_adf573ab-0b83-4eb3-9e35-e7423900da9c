# Minimal Configuration for Testing Core Functionality
spring.application.name=healthconnect-backend
server.port=8080
server.address=0.0.0.0

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true

# H2 Database Configuration
spring.datasource.url=jdbc:h2:mem:healthconnect
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# H2 Console (for testing)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# JWT Configuration
jwt.secret=mySecretKey
jwt.expiration=86400000

# Logging
logging.level.com.healthconnect=INFO
logging.level.com.healthconnect.config.JwtAuthenticationFilter=DEBUG
logging.level.com.healthconnect.service.JwtService=DEBUG
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web.cors=WARN

# Disable problematic features for testing
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration

# Gemini API Configuration
gemini.api.url=https://us-central1-said-eb2f5.cloudfunctions.net/gemini_medical_assistant
gemini.api.timeout=30000
