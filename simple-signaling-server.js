#!/usr/bin/env node

/**
 * Simple WebSocket Signaling Server for Video Calls
 * Run with: node simple-signaling-server.js
 */

const WebSocket = require('ws');
const http = require('http');

const PORT = 8082;

// Create HTTP server
const server = http.createServer();

// Create WebSocket server
const wss = new WebSocket.Server({ server });

// Store rooms and connections
const rooms = new Map();

console.log(`🚀 Simple Video Call Signaling Server`);
console.log(`📡 Starting WebSocket server on port ${PORT}...`);

wss.on('connection', (ws) => {
    console.log('📱 New client connected');
    
    let currentRoom = null;
    let clientId = generateId();
    
    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data.toString());
            console.log(`📨 Message from ${clientId}:`, message.type);
            
            switch (message.type) {
                case 'join':
                    handleJoin(ws, message.room, clientId);
                    currentRoom = message.room;
                    break;
                    
                case 'offer':
                case 'answer':
                case 'ice-candidate':
                    handleSignaling(message, currentRoom, clientId);
                    break;
                    
                default:
                    console.log('❓ Unknown message type:', message.type);
            }
        } catch (error) {
            console.error('❌ Error parsing message:', error);
        }
    });
    
    ws.on('close', () => {
        console.log(`📱 Client ${clientId} disconnected`);
        if (currentRoom) {
            handleLeave(currentRoom, clientId);
        }
    });
    
    ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
    });
    
    // Store client info
    ws.clientId = clientId;
});

function handleJoin(ws, roomId, clientId) {
    console.log(`🏠 Client ${clientId} joining room: ${roomId}`);
    
    // Create room if it doesn't exist
    if (!rooms.has(roomId)) {
        rooms.set(roomId, new Map());
        console.log(`🏠 Created new room: ${roomId}`);
    }
    
    const room = rooms.get(roomId);
    
    // Notify existing users about new user
    room.forEach((client, id) => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
                type: 'user-joined',
                userId: clientId
            }));
        }
    });
    
    // Add client to room
    room.set(clientId, ws);
    
    console.log(`✅ Client ${clientId} joined room ${roomId}. Room size: ${room.size}`);
    
    // Send confirmation to client
    ws.send(JSON.stringify({
        type: 'joined',
        room: roomId,
        clientId: clientId,
        roomSize: room.size
    }));
}

function handleSignaling(message, roomId, senderId) {
    if (!roomId || !rooms.has(roomId)) {
        console.log('❌ Room not found for signaling:', roomId);
        return;
    }
    
    const room = rooms.get(roomId);
    
    // Forward message to all other clients in the room
    room.forEach((client, clientId) => {
        if (clientId !== senderId && client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
                ...message,
                senderId: senderId
            }));
        }
    });
    
    console.log(`📡 Forwarded ${message.type} from ${senderId} to ${room.size - 1} clients`);
}

function handleLeave(roomId, clientId) {
    if (!rooms.has(roomId)) return;
    
    const room = rooms.get(roomId);
    room.delete(clientId);
    
    console.log(`👋 Client ${clientId} left room ${roomId}. Room size: ${room.size}`);
    
    // Notify remaining clients
    room.forEach((client, id) => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
                type: 'user-left',
                userId: clientId
            }));
        }
    });
    
    // Clean up empty rooms
    if (room.size === 0) {
        rooms.delete(roomId);
        console.log(`🗑️ Deleted empty room: ${roomId}`);
    }
}

function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

// Handle server shutdown gracefully
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down signaling server...');
    
    // Close all WebSocket connections
    wss.clients.forEach((ws) => {
        ws.close();
    });
    
    server.close(() => {
        console.log('✅ Server shut down gracefully');
        process.exit(0);
    });
});

// Start the server
server.listen(PORT, () => {
    console.log(`✅ Signaling server running on ws://localhost:${PORT}`);
    console.log(`📋 Rooms: ${rooms.size}`);
    console.log(`👥 Connected clients: ${wss.clients.size}`);
    console.log('\n🎥 Ready for video calls!');
    console.log('💡 To test: Open http://localhost:4200/video-call/test-room in two browser tabs');
});

// Log server stats every 30 seconds
setInterval(() => {
    console.log(`📊 Stats - Rooms: ${rooms.size}, Clients: ${wss.clients.size}`);
}, 30000);
