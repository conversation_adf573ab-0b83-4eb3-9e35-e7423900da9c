<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Video Consultation Data</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Fix Video Consultation Data</h1>
            <p>This will fix the "undefined undefined" issue and create proper test data</p>
        </div>

        <div>
            <button class="button" onclick="fixAllData()">🚀 Fix All Issues</button>
            <button class="button" onclick="testVideoCall()">🎥 Test Video Call</button>
            <button class="button success" onclick="openFrontend()">📱 Open Frontend</button>
        </div>

        <div id="status"></div>
        <div class="log" id="log"></div>
    </div>

    <script>
        const BACKEND_URL = 'http://localhost:8081/api';
        const FRONTEND_URL = 'http://localhost:4200';
        
        let doctorToken = '';
        let patientToken = '';
        let appointmentId = '';
        let consultationId = '';

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : '📋';
            const logEntry = `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'info') {
            const element = document.getElementById('status');
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                log(`Request failed: ${error.message}`, 'error');
                throw error;
            }
        }

        async function createTestUsers() {
            log('👥 Creating test users with proper names...');
            
            const doctorData = {
                fullName: "Dr. John Smith",
                email: "<EMAIL>",
                password: "password123",
                role: "DOCTOR",
                specialization: "General Medicine",
                licenseNumber: "DOC123456",
                yearsOfExperience: 10,
                consultationFee: 100.0
            };

            const patientData = {
                fullName: "Jane Doe",
                email: "<EMAIL>",
                password: "password123",
                role: "PATIENT"
            };

            try {
                await makeRequest(`${BACKEND_URL}/auth/register`, {
                    method: 'POST',
                    body: JSON.stringify(doctorData)
                });
                log('✅ Doctor user created: Dr. John Smith', 'success');
            } catch (error) {
                log('Doctor user already exists or creation failed');
            }

            try {
                await makeRequest(`${BACKEND_URL}/auth/register`, {
                    method: 'POST',
                    body: JSON.stringify(patientData)
                });
                log('✅ Patient user created: Jane Doe', 'success');
            } catch (error) {
                log('Patient user already exists or creation failed');
            }
        }

        async function authenticateUsers() {
            log('🔐 Authenticating users...');
            
            try {
                const doctorAuth = await makeRequest(`${BACKEND_URL}/auth/login`, {
                    method: 'POST',
                    body: JSON.stringify({
                        email: "<EMAIL>",
                        password: "password123"
                    })
                });
                doctorToken = doctorAuth.token;
                log('✅ Doctor authenticated successfully', 'success');
            } catch (error) {
                log('❌ Doctor authentication failed', 'error');
                return false;
            }

            try {
                const patientAuth = await makeRequest(`${BACKEND_URL}/auth/login`, {
                    method: 'POST',
                    body: JSON.stringify({
                        email: "<EMAIL>",
                        password: "password123"
                    })
                });
                patientToken = patientAuth.token;
                log('✅ Patient authenticated successfully', 'success');
            } catch (error) {
                log('❌ Patient authentication failed', 'error');
                return false;
            }

            return true;
        }

        async function createTestAppointment() {
            log('📅 Creating test appointment...');
            
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            
            const appointmentData = {
                doctorId: 1,
                patientId: 2,
                date: tomorrow.toISOString().split('T')[0],
                startTime: "14:00:00",
                endTime: "14:30:00",
                type: "VIDEO_CALL",
                reasonForVisit: "Video consultation test",
                status: "SCHEDULED"
            };

            try {
                const appointment = await makeRequest(`${BACKEND_URL}/appointments`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${patientToken}`
                    },
                    body: JSON.stringify(appointmentData)
                });
                
                appointmentId = appointment.id;
                log(`✅ Appointment created with ID: ${appointmentId}`, 'success');
                return true;
            } catch (error) {
                log('❌ Failed to create appointment', 'error');
                return false;
            }
        }

        async function createVideoConsultation() {
            log('🎥 Creating video consultation...');
            
            try {
                const consultation = await makeRequest(`${BACKEND_URL}/video-consultation/create`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${doctorToken}`
                    },
                    body: JSON.stringify({
                        appointmentId: appointmentId,
                        type: "ROUTINE_CHECKUP"
                    })
                });
                
                consultationId = consultation.id;
                log(`✅ Video consultation created - ID: ${consultationId}, Room: ${consultation.roomId}`, 'success');
                return true;
            } catch (error) {
                log('❌ Failed to create video consultation', 'error');
                return false;
            }
        }

        async function fixAllData() {
            updateStatus('🔧 Fixing all video consultation issues...', 'info');
            log('🏥 Starting comprehensive fix...');
            
            try {
                await createTestUsers();
                const authSuccess = await authenticateUsers();
                if (!authSuccess) {
                    updateStatus('❌ Authentication failed', 'error');
                    return;
                }
                
                await createTestAppointment();
                await createVideoConsultation();
                
                log('🎉 All issues fixed successfully!', 'success');
                updateStatus('✅ Video consultation is now ready! Refresh the frontend page.', 'success');
                
                // Auto-refresh the frontend
                setTimeout(() => {
                    log('🔄 Auto-refreshing frontend...');
                    window.open(`${FRONTEND_URL}/telemedicine/consultations`, '_blank');
                }, 2000);
                
            } catch (error) {
                log(`❌ Fix failed: ${error.message}`, 'error');
                updateStatus('❌ Fix failed. Check the log for details.', 'error');
            }
        }

        async function testVideoCall() {
            if (!consultationId) {
                updateStatus('❌ Please run "Fix All Issues" first', 'error');
                return;
            }
            
            log('🧪 Testing video call...');
            
            const doctorUrl = `${FRONTEND_URL}/telemedicine/room/${consultationId}?role=doctor`;
            const patientUrl = `${FRONTEND_URL}/telemedicine/room/${consultationId}?role=patient`;
            
            log(`🩺 Doctor URL: ${doctorUrl}`);
            log(`🤒 Patient URL: ${patientUrl}`);
            
            window.open(doctorUrl, '_blank');
            setTimeout(() => {
                window.open(patientUrl, '_blank');
            }, 1000);
            
            updateStatus('✅ Video call windows opened', 'success');
        }

        function openFrontend() {
            log('📱 Opening frontend...');
            window.open(`${FRONTEND_URL}/telemedicine/consultations`, '_blank');
        }

        // Initialize
        log('🔧 Video Consultation Data Fix Tool loaded');
        log('📋 Click "Fix All Issues" to resolve the undefined names problem');
    </script>
</body>
</html>
