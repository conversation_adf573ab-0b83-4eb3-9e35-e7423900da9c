// Modern Dashboard Styles
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// Loading State
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .loading-content {
    text-align: center;
    color: white;

    .modern-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 2rem;
    }

    .loading-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .loading-subtitle {
      font-size: 1rem;
      opacity: 0.8;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Error State
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);

  .error-content {
    text-align: center;
    color: white;
    max-width: 400px;
    padding: 2rem;

    .error-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    .error-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .error-message {
      font-size: 1rem;
      opacity: 0.9;
      margin-bottom: 2rem;
    }
  }
}

// Dashboard Content
.dashboard-content {
  padding: 0;
  background: transparent;
}

// Modern Header
.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 2rem 2rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
  }

  .header-content {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
  }

  .welcome-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;

    .greeting-text {
      .welcome-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        background: linear-gradient(45deg, #fff, #e3f2fd);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .welcome-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
      }
    }

    .header-actions {
      .btn-modern {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          border-color: rgba(255, 255, 255, 0.5);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        i {
          margin-right: 0.5rem;
        }
      }
    }
  }

  .header-stats {
    display: flex;
    gap: 2rem;

    .stat-item {
      text-align: center;

      .stat-value {
        font-size: 2rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.25rem;
      }

      .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .stat-divider {
      width: 1px;
      background: rgba(255, 255, 255, 0.3);
      margin: 0.5rem 0;
    }
  }
}

// Section Styles
.section-header {
  margin-bottom: 2rem;
  padding: 0 2rem;

  .section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;

    .section-icon {
      margin-right: 0.75rem;
      color: #667eea;
      font-size: 1.5rem;
    }
  }

  .section-subtitle {
    color: #7f8c8d;
    font-size: 1rem;
    margin: 0;
  }
}

// Modern Card Styles
.modern-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 2rem;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }

  .card-header-modern {
    padding: 2rem 2rem 1rem;
    background: transparent;
    border: none;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .header-left {
      display: flex;
      align-items: flex-start;
      gap: 1rem;

      .feature-icon {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;

        &.prescription-icon {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.appointments-icon {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
      }

      .header-text {
        .card-title-modern {
          font-size: 1.5rem;
          font-weight: 700;
          color: #2c3e50;
          margin-bottom: 0.25rem;
        }

        .card-subtitle-modern {
          color: #7f8c8d;
          font-size: 0.95rem;
          margin: 0;
        }
      }
    }

    .btn-close-modern {
      background: #f8f9fa;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #6c757d;
      transition: all 0.3s ease;

      &:hover {
        background: #e9ecef;
        color: #495057;
        transform: rotate(90deg);
      }
    }
  }

  .card-content-modern {
    padding: 0 2rem 2rem;
  }
}

// Button Styles
.btn-modern {
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;

  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
  }

  &.btn-outline {
    background: transparent;
    border: 2px solid #e9ecef;
    color: #6c757d;

    &:hover {
      background: #f8f9fa;
      border-color: #dee2e6;
      color: #495057;
    }
  }

  &.btn-refresh {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

// Metrics Section
.metrics-section {
  padding: 0 2rem 3rem;

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;

    .metric-card {
      background: white;
      border-radius: 16px;
      padding: 1.5rem;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      }

      .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        .metric-icon {
          width: 50px;
          height: 50px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.25rem;
          color: white;

          &.text-primary-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
          &.text-success-bg { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
          &.text-warning-bg { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
          &.text-info-bg { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        }

        .metric-status {
          .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &.badge-success {
              background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
              color: white;
            }

            &.badge-warning {
              background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
              color: white;
            }

            &.badge-danger {
              background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
              color: white;
            }
          }
        }
      }

      .metric-content {
        .metric-name {
          font-size: 1rem;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 0.5rem;
        }

        .metric-value-container {
          display: flex;
          align-items: baseline;
          gap: 0.25rem;
          margin-bottom: 1rem;

          .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1;
          }

          .metric-unit {
            font-size: 1rem;
            color: #7f8c8d;
            font-weight: 500;
          }
        }
      }

      .metric-trend {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .trend-text {
          font-size: 0.85rem;
          color: #7f8c8d;
        }
      }
    }
  }
}

// Actions Section
.actions-section {
  padding: 0 2rem 3rem;

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;

    .action-card {
      background: white;
      border-radius: 16px;
      padding: 1.5rem;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 1rem;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);

        .action-arrow {
          transform: translateX(5px);
        }
      }

      .action-icon-container {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        &.bg-primary-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        &.bg-success-gradient { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
        &.bg-warning-gradient { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        &.bg-info-gradient { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }

        .action-icon {
          font-size: 1.5rem;
          color: white;
        }
      }

      .action-content {
        flex: 1;

        .action-title {
          font-size: 1.1rem;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 0.25rem;
        }

        .action-description {
          font-size: 0.9rem;
          color: #7f8c8d;
          margin: 0;
          line-height: 1.4;
        }
      }

      .action-arrow {
        color: #bdc3c7;
        font-size: 1.25rem;
        transition: all 0.3s ease;
      }
    }
  }
}

// Insurance Section
.insurance-section {
  padding: 0 2rem 3rem;
}

// Prescription Section
.prescription-section {
  padding: 0 2rem 3rem;
}

// Appointments Section
.appointments-section {
  padding: 0 2rem 3rem;

  .appointments-list {
    .appointment-item-modern {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      padding: 1.5rem;
      background: #f8f9fa;
      border-radius: 12px;
      margin-bottom: 1rem;
      transition: all 0.3s ease;

      &:hover {
        background: #e9ecef;
        transform: translateX(5px);
      }

      .appointment-date-modern {
        .date-container {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 1rem;
          border-radius: 12px;
          text-align: center;
          min-width: 70px;

          .date-day {
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            display: block;
          }

          .date-month {
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            opacity: 0.9;
          }
        }
      }

      .appointment-details {
        flex: 1;

        .doctor-name {
          font-size: 1.1rem;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 0.25rem;
        }

        .specialization {
          color: #7f8c8d;
          font-size: 0.9rem;
          margin-bottom: 0.5rem;
        }

        .appointment-time {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: #6c757d;
          font-size: 0.85rem;

          i {
            color: #667eea;
          }
        }
      }

      .appointment-status {
        .status-badge-modern {
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;

          &.badge-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
          }

          &.badge-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
          }

          &.badge-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }
        }
      }
    }
  }
}

// Empty States
.empty-state {
  text-align: center;
  padding: 3rem 2rem;

  .empty-icon {
    font-size: 4rem;
    color: #bdc3c7;
    margin-bottom: 1.5rem;
  }

  .empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
  }

  .empty-subtitle {
    color: #7f8c8d;
    font-size: 1rem;
    margin-bottom: 2rem;
    line-height: 1.5;
  }
}

// Legacy Card Overrides for Existing Components
.card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  .card-header {
    background: transparent;
    border-bottom: 1px solid #f1f3f4;
    padding: 1.5rem 1.5rem 1rem;

    h6 {
      font-weight: 600;
      color: #2c3e50;
      margin: 0;
    }
  }

  .card-body {
    padding: 1.5rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .dashboard-header {
    padding: 2rem 1rem 1.5rem;

    .welcome-section {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;

      .greeting-text .welcome-title {
        font-size: 2rem;
      }
    }

    .header-stats {
      gap: 1rem;

      .stat-item .stat-value {
        font-size: 1.5rem;
      }
    }
  }

  .section-header,
  .metrics-section,
  .actions-section,
  .insurance-section,
  .prescription-section,
  .appointments-section {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .metrics-grid,
  .actions-grid {
    grid-template-columns: 1fr;
  }

  .appointment-item-modern {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    .appointment-date-modern .date-container {
      min-width: auto;
      padding: 0.75rem 1rem;
    }
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    .welcome-section .greeting-text .welcome-title {
      font-size: 1.75rem;
    }

    .header-stats {
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  .action-card {
    flex-direction: column;
    text-align: center;

    .action-content {
      text-align: center;
    }
  }
}

.text-primary {
  color: #0d6efd !important;
}

.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

// Responsive adjustments
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .display-6 {
    font-size: 2rem;
  }
}
