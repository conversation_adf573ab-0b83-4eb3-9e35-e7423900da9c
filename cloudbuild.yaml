# Google Cloud Build configuration for HealthConnect
steps:
  # Build Backend Docker Image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/said-eb2f5/healthconnect-backend:$BUILD_ID'
      - '-t'
      - 'gcr.io/said-eb2f5/healthconnect-backend:latest'
      - './backend'
    id: 'build-backend'

  # Build Frontend Docker Image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/said-eb2f5/healthconnect-frontend:$BUILD_ID'
      - '-t'
      - 'gcr.io/said-eb2f5/healthconnect-frontend:latest'
      - './frontend'
    id: 'build-frontend'

  # Push Backend Image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/said-eb2f5/healthconnect-backend:$BUILD_ID'
    id: 'push-backend'
    waitFor: ['build-backend']

  # Push Frontend Image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/said-eb2f5/healthconnect-frontend:$BUILD_ID'
    id: 'push-frontend'
    waitFor: ['build-frontend']

  # Deploy Backend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'healthconnect-backend'
      - '--image'
      - 'gcr.io/said-eb2f5/healthconnect-backend:$BUILD_ID'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '8080'
      - '--memory'
      - '2Gi'
      - '--cpu'
      - '2'
      - '--max-instances'
      - '10'
      - '--set-env-vars'
      - 'SPRING_PROFILES_ACTIVE=prod,SPRING_DATASOURCE_URL=jdbc:h2:mem:healthconnect,SPRING_DATASOURCE_DRIVER_CLASS_NAME=org.h2.Driver,SPRING_DATASOURCE_USERNAME=sa,SPRING_DATASOURCE_PASSWORD=,SPRING_JPA_DATABASE_PLATFORM=org.hibernate.dialect.H2Dialect,SPRING_H2_CONSOLE_ENABLED=false,CORS_ALLOWED_ORIGINS=https://healthconnect-frontend-*-uc.a.run.app'
    id: 'deploy-backend'
    waitFor: ['push-backend']

  # Deploy Frontend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'healthconnect-frontend'
      - '--image'
      - 'gcr.io/said-eb2f5/healthconnect-frontend:$BUILD_ID'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '80'
      - '--memory'
      - '512Mi'
      - '--cpu'
      - '1'
      - '--max-instances'
      - '5'
    id: 'deploy-frontend'
    waitFor: ['push-frontend', 'deploy-backend']

# Store images in Container Registry
images:
  - 'gcr.io/said-eb2f5/healthconnect-backend:$BUILD_ID'
  - 'gcr.io/said-eb2f5/healthconnect-frontend:$BUILD_ID'
  - 'gcr.io/said-eb2f5/healthconnect-backend:latest'
  - 'gcr.io/said-eb2f5/healthconnect-frontend:latest'

# Build options
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: 100
  logging: CLOUD_LOGGING_ONLY

# Build timeout
timeout: '1800s'
