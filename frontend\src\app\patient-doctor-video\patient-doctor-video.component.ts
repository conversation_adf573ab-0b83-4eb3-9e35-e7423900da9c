import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../core/services/auth.service';
import { HttpClient } from '@angular/common/http';
import { Subscription, interval } from 'rxjs';
import { FormsModule } from '@angular/forms';

interface VideoConsultation {
  id: number;
  roomId: string;
  patientName: string;
  doctorName: string;
  scheduledTime: string;
  status: string;
}

@Component({
  selector: 'app-patient-doctor-video',
  template: `
    <div class="video-consultation-container">
      <!-- Header -->
      <div class="header">
        <div class="consultation-info">
          <h2>🏥 Video Consultation</h2>
          <div class="participants">
            <span class="participant patient">
              👤 Patient: {{ consultation?.patientName || 'Loading...' }}
            </span>
            <span class="participant doctor">
              👨‍⚕️ Doctor: {{ consultation?.doctorName || 'Loading...' }}
            </span>
          </div>
          <div class="role-indicator">
            You are joining as: <strong>{{ currentUserRole }}</strong>
          </div>
        </div>
        <div class="consultation-status" [ngClass]="statusClass">
          {{ statusMessage }}
        </div>
      </div>

      <!-- Video Section -->
      <div class="video-grid">
        <div class="video-container local">
          <div class="video-header">
            <h4>{{ currentUserRole === 'PATIENT' ? 'You (Patient)' : 'You (Doctor)' }}</h4>
            <div class="video-status">
              {{ localVideoActive ? '📹 Video On' : '📹 Video Off' }}
              {{ localAudioActive ? '🎤 Mic On' : '🔇 Mic Off' }}
            </div>
          </div>
          <video #localVideo autoplay muted playsinline></video>
        </div>

        <div class="video-container remote">
          <div class="video-header">
            <h4>{{ currentUserRole === 'PATIENT' ? 'Doctor' : 'Patient' }}</h4>
            <div class="video-status" *ngIf="remoteConnected">
              📹 Connected
            </div>
          </div>
          <video #remoteVideo autoplay playsinline></video>
          <div class="waiting-message" *ngIf="!remoteConnected && isConnected">
            <div class="spinner"></div>
            <p>Waiting for {{ currentUserRole === 'PATIENT' ? 'doctor' : 'patient' }} to join...</p>
          </div>
        </div>
      </div>

      <!-- Controls -->
      <div class="controls">
        <button 
          class="btn btn-success" 
          (click)="joinConsultation()"
          [disabled]="isConnected">
          {{ isConnected ? 'Connected' : 'Join Consultation' }}
        </button>
        
        <button 
          class="btn btn-warning" 
          (click)="toggleVideo()"
          [disabled]="!isConnected">
          {{ localVideoActive ? 'Turn Off Video' : 'Turn On Video' }}
        </button>
        
        <button 
          class="btn btn-warning" 
          (click)="toggleAudio()"
          [disabled]="!isConnected">
          {{ localAudioActive ? 'Mute' : 'Unmute' }}
        </button>
        
        <button 
          class="btn btn-danger" 
          (click)="endConsultation()">
          End Consultation
        </button>
      </div>

      <!-- Chat Section -->
      <div class="chat-section" *ngIf="isConnected">
        <h4>💬 Chat</h4>
        <div class="chat-messages" #chatMessages>
          <div *ngFor="let message of chatMessages" class="chat-message" [ngClass]="message.sender">
            <strong>{{ message.senderName }}:</strong> {{ message.text }}
            <span class="timestamp">{{ message.timestamp | date:'short' }}</span>
          </div>
        </div>
        <div class="chat-input">
          <input 
            type="text" 
            [(ngModel)]="newMessage" 
            (keyup.enter)="sendMessage()"
            placeholder="Type a message..."
            class="form-control">
          <button class="btn btn-primary" (click)="sendMessage()">Send</button>
        </div>
      </div>

      <!-- Notifications -->
      <div class="notifications" *ngIf="notifications.length > 0">
        <div *ngFor="let notification of notifications" class="notification" [ngClass]="notification.type">
          {{ notification.message }}
          <button class="close-btn" (click)="dismissNotification(notification)">&times;</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .video-consultation-container {
      padding: 20px;
      max-width: 1400px;
      margin: 0 auto;
      font-family: Arial, sans-serif;
    }
    
    .header {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 20px;
      border-left: 4px solid #007bff;
    }
    
    .consultation-info h2 {
      margin: 0 0 15px 0;
      color: #2c3e50;
    }
    
    .participants {
      display: flex;
      gap: 20px;
      margin-bottom: 10px;
    }
    
    .participant {
      padding: 8px 12px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
    }
    
    .participant.patient {
      background: #e3f2fd;
      color: #1976d2;
    }
    
    .participant.doctor {
      background: #f3e5f5;
      color: #7b1fa2;
    }
    
    .role-indicator {
      font-size: 16px;
      color: #495057;
    }
    
    .consultation-status {
      padding: 10px;
      border-radius: 5px;
      margin-top: 15px;
      text-align: center;
      font-weight: bold;
    }
    
    .consultation-status.waiting {
      background: #fff3cd;
      color: #856404;
    }
    
    .consultation-status.connected {
      background: #d4edda;
      color: #155724;
    }
    
    .consultation-status.error {
      background: #f8d7da;
      color: #721c24;
    }
    
    .video-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .video-container {
      background: #000;
      border-radius: 10px;
      overflow: hidden;
      position: relative;
    }
    
    .video-header {
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .video-header h4 {
      margin: 0;
      font-size: 16px;
    }
    
    .video-status {
      font-size: 12px;
    }
    
    video {
      width: 100%;
      height: 300px;
      object-fit: cover;
    }
    
    .waiting-message {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: white;
    }
    
    .spinner {
      border: 3px solid #f3f3f3;
      border-top: 3px solid #007bff;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      animation: spin 1s linear infinite;
      margin: 0 auto 10px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .controls {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .btn {
      margin: 5px;
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
    }
    
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: black; }
    .btn-danger { background: #dc3545; color: white; }
    .btn-primary { background: #007bff; color: white; }
    .btn:disabled { opacity: 0.6; cursor: not-allowed; }
    
    .chat-section {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 20px;
    }
    
    .chat-messages {
      height: 200px;
      overflow-y: auto;
      border: 1px solid #dee2e6;
      border-radius: 5px;
      padding: 10px;
      background: white;
      margin-bottom: 10px;
    }
    
    .chat-message {
      margin-bottom: 10px;
      padding: 8px;
      border-radius: 5px;
    }
    
    .chat-message.patient {
      background: #e3f2fd;
      margin-right: 20%;
    }
    
    .chat-message.doctor {
      background: #f3e5f5;
      margin-left: 20%;
    }
    
    .timestamp {
      font-size: 11px;
      color: #6c757d;
      float: right;
    }
    
    .chat-input {
      display: flex;
      gap: 10px;
    }
    
    .chat-input input {
      flex: 1;
      padding: 8px;
      border: 1px solid #ced4da;
      border-radius: 4px;
    }
    
    .notifications {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }
    
    .notification {
      background: #007bff;
      color: white;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 10px;
      position: relative;
      min-width: 300px;
    }
    
    .notification.success { background: #28a745; }
    .notification.warning { background: #ffc107; color: black; }
    .notification.error { background: #dc3545; }
    
    .close-btn {
      position: absolute;
      top: 5px;
      right: 10px;
      background: none;
      border: none;
      color: inherit;
      font-size: 18px;
      cursor: pointer;
    }
    
    @media (max-width: 768px) {
      .video-grid {
        grid-template-columns: 1fr;
      }
      
      .participants {
        flex-direction: column;
        gap: 10px;
      }
    }
  `]
})
export class PatientDoctorVideoComponent implements OnInit, OnDestroy {
  @ViewChild('localVideo', { static: false }) localVideo!: ElementRef<HTMLVideoElement>;
  @ViewChild('remoteVideo', { static: false }) remoteVideo!: ElementRef<HTMLVideoElement>;
  @ViewChild('chatMessages', { static: false }) chatMessagesContainer!: ElementRef<HTMLDivElement>;

  consultationId: string = '';
  consultation: VideoConsultation | null = null;
  currentUser: any;
  currentUserRole: string = '';
  
  isConnected = false;
  remoteConnected = false;
  localVideoActive = true;
  localAudioActive = true;
  
  statusMessage = 'Loading consultation details...';
  statusClass = 'waiting';
  
  chatMessages: any[] = [];
  newMessage = '';
  notifications: any[] = [];
  
  private localStream: MediaStream | null = null;
  private subscriptions: Subscription[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private http: HttpClient
  ) {
    this.currentUser = this.authService.getCurrentUser();
    this.currentUserRole = this.currentUser?.role || 'PATIENT';
  }

  ngOnInit(): void {
    this.consultationId = this.route.snapshot.params['id'] || 'demo-consultation';
    this.loadConsultationDetails();
    this.setupNotificationPolling();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.cleanup();
  }

  private loadConsultationDetails(): void {
    // For demo purposes, create mock consultation data
    this.consultation = {
      id: 1,
      roomId: this.consultationId,
      patientName: 'John Smith',
      doctorName: 'Dr. Sarah Johnson',
      scheduledTime: new Date().toISOString(),
      status: 'SCHEDULED'
    };
    
    this.statusMessage = 'Ready to join consultation';
    this.statusClass = 'waiting';
    
    // Show notification for the appropriate role
    if (this.currentUserRole === 'PATIENT') {
      this.addNotification('Your video consultation is ready. Click "Join Consultation" to begin.', 'success');
    } else {
      this.addNotification('Patient consultation is ready. Click "Join Consultation" to begin.', 'success');
    }
  }

  private setupNotificationPolling(): void {
    // Poll for consultation updates every 10 seconds
    const pollSubscription = interval(10000).subscribe(() => {
      if (!this.isConnected) {
        this.checkForUpdates();
      }
    });
    
    this.subscriptions.push(pollSubscription);
  }

  private checkForUpdates(): void {
    // In a real implementation, this would check for consultation updates
    console.log('Checking for consultation updates...');
  }

  async joinConsultation(): Promise<void> {
    try {
      this.statusMessage = 'Joining consultation...';
      this.statusClass = 'waiting';

      // Get user media
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });

      // Display local video
      this.localVideo.nativeElement.srcObject = this.localStream;

      this.isConnected = true;
      this.statusMessage = `✅ Connected as ${this.currentUserRole}`;
      this.statusClass = 'connected';

      // Add welcome message to chat
      this.addChatMessage(
        'system',
        'System',
        `${this.currentUserRole === 'PATIENT' ? 'Patient' : 'Doctor'} has joined the consultation`
      );

      // Simulate remote connection after a delay
      setTimeout(() => {
        this.simulateRemoteConnection();
      }, 3000);

    } catch (error) {
      console.error('Error joining consultation:', error);
      this.statusMessage = 'Failed to join consultation. Please check camera/microphone permissions.';
      this.statusClass = 'error';
      this.addNotification('Failed to access camera or microphone. Please check permissions.', 'error');
    }
  }

  private simulateRemoteConnection(): void {
    // Simulate the other participant joining
    this.remoteConnected = true;
    const otherRole = this.currentUserRole === 'PATIENT' ? 'Doctor' : 'Patient';
    this.addNotification(`${otherRole} has joined the consultation`, 'success');
    this.addChatMessage(
      'system',
      'System',
      `${otherRole} has joined the consultation`
    );
  }

  toggleVideo(): void {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        this.localVideoActive = videoTrack.enabled;
        
        this.addChatMessage(
          'system',
          'System',
          `${this.currentUserRole} ${this.localVideoActive ? 'turned on' : 'turned off'} their video`
        );
      }
    }
  }

  toggleAudio(): void {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        this.localAudioActive = audioTrack.enabled;
        
        this.addChatMessage(
          'system',
          'System',
          `${this.currentUserRole} ${this.localAudioActive ? 'unmuted' : 'muted'} their microphone`
        );
      }
    }
  }

  sendMessage(): void {
    if (this.newMessage.trim()) {
      this.addChatMessage(
        this.currentUserRole.toLowerCase(),
        this.currentUserRole === 'PATIENT' ? this.consultation?.patientName || 'Patient' : this.consultation?.doctorName || 'Doctor',
        this.newMessage
      );
      this.newMessage = '';
    }
  }

  private addChatMessage(sender: string, senderName: string, text: string): void {
    this.chatMessages.push({
      sender,
      senderName,
      text,
      timestamp: new Date()
    });
    
    // Scroll to bottom
    setTimeout(() => {
      if (this.chatMessagesContainer) {
        this.chatMessagesContainer.nativeElement.scrollTop = this.chatMessagesContainer.nativeElement.scrollHeight;
      }
    }, 100);
  }

  endConsultation(): void {
    this.cleanup();
    this.addNotification('Consultation ended', 'warning');
    
    // Redirect back to dashboard after a delay
    setTimeout(() => {
      this.router.navigate(['/dashboard']);
    }, 2000);
  }

  private cleanup(): void {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.localVideo?.nativeElement) {
      this.localVideo.nativeElement.srcObject = null;
    }

    if (this.remoteVideo?.nativeElement) {
      this.remoteVideo.nativeElement.srcObject = null;
    }

    this.isConnected = false;
    this.remoteConnected = false;
  }

  addNotification(message: string, type: string): void {
    const notification = {
      id: Date.now(),
      message,
      type
    };
    
    this.notifications.push(notification);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
      this.dismissNotification(notification);
    }, 5000);
  }

  dismissNotification(notification: any): void {
    const index = this.notifications.indexOf(notification);
    if (index > -1) {
      this.notifications.splice(index, 1);
    }
  }
}
