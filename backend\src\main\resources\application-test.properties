# Test Configuration for Local Development
spring.application.name=healthconnect-backend
server.port=8080
server.address=0.0.0.0

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true

# H2 Database Configuration
spring.datasource.url=jdbc:h2:mem:healthconnect
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# H2 Console (for testing)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
jwt.secret=mySecretKey
jwt.expiration=86400000

# Logging
logging.level.com.healthconnect=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web.cors=DEBUG

# Disable problematic features for testing
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration
