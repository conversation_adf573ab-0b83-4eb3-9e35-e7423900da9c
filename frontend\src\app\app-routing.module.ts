import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './core/guards/auth.guard';
import { VideoCallTestComponent } from './video-call-test/video-call-test.component';
import { SimpleVideoCallComponent } from './simple-video-call/simple-video-call.component';
import { BasicVideoTestComponent } from './basic-video-test/basic-video-test.component';
import { PatientDoctorVideoComponent } from './patient-doctor-video/patient-doctor-video.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: '/auth/login',
    pathMatch: 'full'
  },
  {
    path: 'auth',
    loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)
  },
  {
    path: 'patient',
    canActivate: [AuthGuard],
    data: { roles: ['PATIENT'] },
    loadChildren: () => import('./patient/patient.module').then(m => m.PatientModule)
  },
  {
    path: 'doctor',
    canActivate: [AuthGuard],
    data: { roles: ['DOCTOR'] },
    loadChildren: () => import('./doctor/doctor.module').then(m => m.DoctorModule)
  },
  {
    path: 'profile',
    canActivate: [AuthGuard],
    loadChildren: () => import('./profile/profile.module').then(m => m.ProfileModule)
  },
  {
    path: 'appointments',
    canActivate: [AuthGuard],
    data: { roles: ['PATIENT'] },
    loadChildren: () => import('./appointments/appointments.module').then(m => m.AppointmentsModule)
  },
  {
    path: 'chat',
    canActivate: [AuthGuard],
    loadChildren: () => import('./chat/chat.module').then(m => m.ChatModule)
  },
  {
    path: 'ai-health-bot',
    canActivate: [AuthGuard],
    data: { roles: ['PATIENT'] },
    loadChildren: () => import('./ai-health-bot/ai-health-bot.module').then(m => m.AiHealthBotModule)
  },
  {
    path: 'telemedicine',
    canActivate: [AuthGuard],
    loadChildren: () => import('./telemedicine/telemedicine.module').then(m => m.TelemedicineModule)
  },
  {
    path: 'debug',
    canActivate: [AuthGuard],
    loadChildren: () => import('./debug/debug.module').then(m => m.DebugModule)
  },
  {
    path: 'video-test',
    canActivate: [AuthGuard],
    component: VideoCallTestComponent
  },
  {
    path: 'video-call/:roomId',
    canActivate: [AuthGuard],
    component: SimpleVideoCallComponent
  },
  {
    path: 'video-call',
    canActivate: [AuthGuard],
    component: SimpleVideoCallComponent
  },
  {
    path: 'basic-video-test',
    canActivate: [AuthGuard],
    component: BasicVideoTestComponent
  },
  {
    path: 'video-consultation/:id',
    canActivate: [AuthGuard],
    component: PatientDoctorVideoComponent
  },
  {
    path: '**',
    redirectTo: '/auth/login'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
