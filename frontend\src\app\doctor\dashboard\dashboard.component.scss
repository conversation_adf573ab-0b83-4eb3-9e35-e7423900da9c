// Modern Doctor Dashboard Styles
.doctor-dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

// Loading State
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .loading-content {
    text-align: center;
    color: white;

    .modern-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 2rem;
    }

    .loading-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .loading-subtitle {
      font-size: 1rem;
      opacity: 0.8;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Error State
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);

  .error-content {
    text-align: center;
    color: white;
    max-width: 400px;
    padding: 2rem;

    .error-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    .error-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .error-message {
      font-size: 1rem;
      opacity: 0.9;
      margin-bottom: 2rem;
    }
  }
}

// Dashboard Content
.dashboard-content {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

// Doctor Header
.doctor-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 2rem 2rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="medical" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/><path d="M20,25 L30,25 M25,20 L25,30" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23medical)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
  }

  .welcome-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;

    .doctor-info {
      .greeting-text {
        .welcome-title {
          font-size: 2.5rem;
          font-weight: 700;
          margin-bottom: 1rem;
          background: linear-gradient(45deg, #fff, #e3f2fd);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .doctor-details {
          display: flex;
          flex-wrap: wrap;
          gap: 1.5rem;

          .specialization,
          .affiliation,
          .experience {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
            opacity: 0.9;

            i {
              font-size: 1.1rem;
            }
          }
        }
      }
    }

    .header-actions {
      .btn-modern {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          border-color: rgba(255, 255, 255, 0.5);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        i {
          margin-right: 0.5rem;
        }
      }
    }
  }

  .header-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;

    .stat-item {
      text-align: center;
      background: rgba(255, 255, 255, 0.1);
      padding: 1.5rem;
      border-radius: 16px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.5rem;
      }

      .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
        margin-bottom: 0.5rem;
      }

      .stat-change {
        font-size: 0.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.25rem;
      }
    }
  }
}

// Section Styles
.section-header {
  margin-bottom: 2rem;
  padding: 0 2rem;

  .section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;

    .section-icon {
      margin-right: 0.75rem;
      color: #667eea;
      font-size: 1.5rem;
    }
  }

  .section-subtitle {
    color: #7f8c8d;
    font-size: 1rem;
    margin: 0;
  }
}

// Modern Card Styles
.modern-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 2rem;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }

  .card-header-modern {
    padding: 2rem 2rem 1rem;
    background: transparent;
    border: none;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .header-left {
      display: flex;
      align-items: flex-start;
      gap: 1rem;

      .feature-icon {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;

        &.activities-icon {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.actions-icon {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.messages-icon {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
      }

      .header-text {
        .card-title-modern {
          font-size: 1.5rem;
          font-weight: 700;
          color: #2c3e50;
          margin-bottom: 0.25rem;
        }

        .card-subtitle-modern {
          color: #7f8c8d;
          font-size: 0.95rem;
          margin: 0;
        }
      }
    }
  }

  .card-content-modern {
    padding: 0 2rem 2rem;
  }
}

// Button Styles
.btn-modern {
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;

  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
  }

  &.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(86, 171, 47, 0.4);
    }
  }

  &.btn-outline {
    background: transparent;
    border: 2px solid #e9ecef;
    color: #6c757d;

    &:hover {
      background: #f8f9fa;
      border-color: #dee2e6;
      color: #495057;
    }
  }
}

// Schedule Section
.schedule-section {
  padding: 0 2rem 3rem;

  .schedule-content {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .schedule-overview {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .time-indicator {
        .current-time {
          .time {
            font-size: 2.5rem;
            font-weight: 700;
            display: block;
            line-height: 1;
          }

          .date {
            font-size: 1rem;
            opacity: 0.9;
            margin-top: 0.5rem;
          }
        }
      }

      .schedule-actions {
        display: flex;
        gap: 1rem;
      }
    }

    .appointments-timeline {
      padding: 2rem;
    }

    .empty-schedule {
      text-align: center;
      padding: 4rem 2rem;

      .empty-icon {
        font-size: 4rem;
        color: #bdc3c7;
        margin-bottom: 1.5rem;
      }

      .empty-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
      }

      .empty-subtitle {
        color: #7f8c8d;
        font-size: 1rem;
        margin-bottom: 2rem;
        line-height: 1.5;
      }
    }

    .appointment-timeline {
      .appointment-card-modern {
        display: flex;
        align-items: center;
        gap: 2rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 16px;
        margin-bottom: 1rem;
        transition: all 0.3s ease;

        &:hover {
          background: #e9ecef;
          transform: translateX(5px);
        }

        .appointment-time-slot {
          display: flex;
          align-items: center;
          gap: 1rem;
          min-width: 150px;

          .time-display {
            text-align: center;

            .start-time {
              font-size: 1.25rem;
              font-weight: 700;
              color: #2c3e50;
              display: block;
            }

            .duration {
              font-size: 0.85rem;
              color: #7f8c8d;
            }
          }

          .appointment-type-indicator {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;

            &.text-primary {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            &.text-success {
              background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            }
          }
        }

        .appointment-details {
          flex: 1;

          .patient-info {
            .patient-name {
              font-size: 1.25rem;
              font-weight: 600;
              color: #2c3e50;
              margin-bottom: 0.5rem;
            }

            .appointment-meta {
              color: #7f8c8d;
              font-size: 0.9rem;
              margin-bottom: 0.5rem;

              .appointment-type {
                font-weight: 500;
              }

              .appointment-reason {
                font-style: italic;
              }
            }

            .appointment-status {
              .status-badge-modern {
                padding: 0.25rem 0.75rem;
                border-radius: 20px;
                font-size: 0.75rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;

                &.badge {
                  &.bg-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                    color: white;
                  }

                  &.bg-success {
                    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
                    color: white;
                  }

                  &.bg-danger {
                    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
                    color: white;
                  }
                }
              }
            }
          }
        }

        .appointment-actions {
          display: flex;
          gap: 0.5rem;
        }
      }
    }
  }
}

// Activities & Actions Section
.activities-actions-section {
  padding: 0 2rem 3rem;

  .section-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;

    .activities-card,
    .quick-actions-card {
      .activities-timeline {
        .activity-item-modern {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
          padding: 1rem 0;
          border-bottom: 1px solid #f1f3f4;

          &:last-child {
            border-bottom: none;
          }

          .activity-icon-container {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;

            &.text-primary-bg {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            &.text-success-bg {
              background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            }

            &.text-warning-bg {
              background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            }

            &.text-info-bg {
              background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            }
          }

          .activity-content {
            flex: 1;

            .activity-title {
              font-size: 1rem;
              font-weight: 600;
              color: #2c3e50;
              margin-bottom: 0.25rem;
            }

            .activity-description {
              color: #7f8c8d;
              font-size: 0.9rem;
              margin-bottom: 0.5rem;
              line-height: 1.4;
            }

            .activity-time {
              color: #bdc3c7;
              font-size: 0.8rem;
              font-weight: 500;
            }
          }
        }
      }

      .quick-actions-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;

        .quick-action-btn {
          background: #f8f9fa;
          border: 2px solid #e9ecef;
          border-radius: 12px;
          padding: 1.5rem 1rem;
          text-align: center;
          transition: all 0.3s ease;
          cursor: pointer;

          &:hover {
            background: white;
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
          }

          .action-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.25rem;
            color: white;

            &.patients-icon {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            &.appointments-icon {
              background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            }

            &.messages-icon {
              background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            }

            &.reports-icon {
              background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            }
          }

          .action-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: #2c3e50;
          }
        }
      }
    }
  }
}

// Messages Section
.messages-section {
  padding: 0 2rem 3rem;

  .messages-list {
    .message-item-modern {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1.5rem;
      background: #f8f9fa;
      border-radius: 12px;
      margin-bottom: 1rem;
      transition: all 0.3s ease;

      &:hover {
        background: #e9ecef;
        transform: translateX(5px);
      }

      .patient-avatar {
        position: relative;

        .avatar-image {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          object-fit: cover;
          border: 3px solid white;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .online-indicator {
          position: absolute;
          bottom: 2px;
          right: 2px;
          width: 12px;
          height: 12px;
          background: #56ab2f;
          border: 2px solid white;
          border-radius: 50%;
        }
      }

      .message-content {
        flex: 1;

        .message-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.5rem;

          .patient-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
          }

          .message-time {
            color: #7f8c8d;
            font-size: 0.8rem;
          }
        }

        .message-preview {
          color: #7f8c8d;
          font-size: 0.9rem;
          margin-bottom: 0.5rem;
          line-height: 1.4;
        }

        .message-meta {
          .unread-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
          }
        }
      }

      .message-actions {
        flex-shrink: 0;
      }
    }
  }
}

// Empty States
.empty-state {
  text-align: center;
  padding: 3rem 2rem;

  .empty-icon {
    font-size: 4rem;
    color: #bdc3c7;
    margin-bottom: 1.5rem;
  }

  .empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
  }

  .empty-subtitle {
    color: #7f8c8d;
    font-size: 1rem;
    margin-bottom: 2rem;
    line-height: 1.5;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .activities-actions-section .section-grid {
    grid-template-columns: 1fr;
  }

  .header-stats {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .doctor-header {
    padding: 2rem 1rem 1.5rem;

    .welcome-section {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;

      .doctor-info .greeting-text .welcome-title {
        font-size: 2rem;
      }

      .doctor-info .greeting-text .doctor-details {
        flex-direction: column;
        gap: 0.75rem;
      }
    }

    .header-stats {
      grid-template-columns: 1fr !important;
      gap: 1rem;
    }
  }

  .section-header,
  .schedule-section,
  .activities-actions-section,
  .messages-section {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .schedule-content .schedule-overview {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;

    .schedule-actions {
      justify-content: center;
    }
  }

  .appointment-card-modern {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;

    .appointment-time-slot {
      min-width: auto;
      align-self: stretch;
      justify-content: space-between;
    }

    .appointment-actions {
      align-self: stretch;

      .btn-modern {
        flex: 1;
      }
    }
  }

  .quick-actions-grid {
    grid-template-columns: 1fr !important;
  }

  .message-item-modern {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;

    .message-content .message-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
    }

    .message-actions {
      align-self: stretch;

      .btn-modern {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

@media (max-width: 480px) {
  .doctor-header .welcome-section .doctor-info .greeting-text .welcome-title {
    font-size: 1.75rem;
  }

  .appointment-time-slot .time-display .start-time {
    font-size: 1rem;
  }

  .patient-name {
    font-size: 1rem !important;
  }
}
