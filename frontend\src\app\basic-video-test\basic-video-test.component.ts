import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';

@Component({
  selector: 'app-basic-video-test',
  template: `
    <div class="container">
      <div class="header">
        <h1>📹 Basic Video Test</h1>
        <p>Simple camera and microphone test</p>
      </div>

      <div class="status-card" [ngClass]="statusClass">
        <h3>{{ statusTitle }}</h3>
        <p>{{ statusMessage }}</p>
      </div>

      <div class="video-section" *ngIf="showVideo">
        <h3>Your Camera</h3>
        <video #videoElement autoplay muted playsinline></video>
      </div>

      <div class="controls">
        <button 
          class="btn btn-primary" 
          (click)="startVideo()"
          [disabled]="isActive">
          {{ isActive ? 'Camera Active' : 'Start Camera' }}
        </button>
        
        <button 
          class="btn btn-warning" 
          (click)="toggleVideo()"
          [disabled]="!isActive">
          {{ videoEnabled ? 'Turn Off Video' : 'Turn On Video' }}
        </button>
        
        <button 
          class="btn btn-warning" 
          (click)="toggleAudio()"
          [disabled]="!isActive">
          {{ audioEnabled ? 'Mute Audio' : 'Unmute Audio' }}
        </button>
        
        <button 
          class="btn btn-danger" 
          (click)="stopVideo()"
          [disabled]="!isActive">
          Stop Camera
        </button>
      </div>

      <div class="info-section">
        <h4>✅ What this tests:</h4>
        <ul>
          <li>Camera access and video display</li>
          <li>Microphone access</li>
          <li>Video/audio toggle controls</li>
          <li>Basic WebRTC functionality</li>
        </ul>
        
        <h4>🔧 Troubleshooting:</h4>
        <ul>
          <li>Make sure to allow camera and microphone permissions</li>
          <li>Check that your camera isn't being used by another application</li>
          <li>Try refreshing the page if video doesn't appear</li>
        </ul>
      </div>
    </div>
  `,
  styles: [`
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .header h1 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .status-card {
      padding: 20px;
      border-radius: 10px;
      text-align: center;
      margin-bottom: 30px;
      border: 2px solid;
    }
    
    .status-card.ready {
      background: #e8f4fd;
      border-color: #3498db;
      color: #2980b9;
    }
    
    .status-card.active {
      background: #d4edda;
      border-color: #28a745;
      color: #155724;
    }
    
    .status-card.error {
      background: #f8d7da;
      border-color: #dc3545;
      color: #721c24;
    }
    
    .video-section {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .video-section h3 {
      margin-bottom: 15px;
      color: #2c3e50;
    }
    
    video {
      width: 100%;
      max-width: 500px;
      height: 375px;
      background: #000;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    
    .controls {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .btn {
      margin: 5px;
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
    }
    
    .btn:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
    
    .btn-primary {
      background: #3498db;
      color: white;
    }
    
    .btn-warning {
      background: #f39c12;
      color: white;
    }
    
    .btn-danger {
      background: #e74c3c;
      color: white;
    }
    
    .info-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      border-left: 4px solid #3498db;
    }
    
    .info-section h4 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .info-section ul {
      margin-bottom: 20px;
    }
    
    .info-section li {
      margin-bottom: 5px;
      color: #555;
    }
  `]
})
export class BasicVideoTestComponent implements OnInit, OnDestroy {
  @ViewChild('videoElement', { static: false }) videoElement!: ElementRef<HTMLVideoElement>;

  isActive = false;
  showVideo = false;
  videoEnabled = true;
  audioEnabled = true;
  
  statusTitle = 'Ready to Test';
  statusMessage = 'Click "Start Camera" to begin testing your video setup';
  statusClass = 'ready';

  private mediaStream: MediaStream | null = null;

  ngOnInit(): void {
    console.log('Basic Video Test component initialized');
  }

  ngOnDestroy(): void {
    this.stopVideo();
  }

  async startVideo(): Promise<void> {
    try {
      this.statusTitle = 'Starting Camera...';
      this.statusMessage = 'Requesting camera and microphone access';
      this.statusClass = 'ready';

      // Request user media
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 }
        },
        audio: true
      });

      // Display video
      this.videoElement.nativeElement.srcObject = this.mediaStream;
      
      this.isActive = true;
      this.showVideo = true;
      this.statusTitle = '✅ Camera Active!';
      this.statusMessage = 'Your camera and microphone are working perfectly. Try the controls below.';
      this.statusClass = 'active';

      console.log('Video started successfully');

    } catch (error) {
      console.error('Error starting video:', error);
      
      this.statusTitle = '❌ Camera Access Failed';
      this.statusClass = 'error';
      
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          this.statusMessage = 'Camera access was denied. Please allow camera permissions and try again.';
        } else if (error.name === 'NotFoundError') {
          this.statusMessage = 'No camera found. Please connect a camera and try again.';
        } else if (error.name === 'NotReadableError') {
          this.statusMessage = 'Camera is being used by another application. Please close other apps and try again.';
        } else {
          this.statusMessage = `Error: ${error.message}`;
        }
      } else {
        this.statusMessage = 'Unknown error occurred while accessing camera.';
      }
    }
  }

  toggleVideo(): void {
    if (this.mediaStream) {
      const videoTrack = this.mediaStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        this.videoEnabled = videoTrack.enabled;
        
        this.statusMessage = this.videoEnabled 
          ? 'Video is now ON' 
          : 'Video is now OFF (audio still active)';
      }
    }
  }

  toggleAudio(): void {
    if (this.mediaStream) {
      const audioTrack = this.mediaStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        this.audioEnabled = audioTrack.enabled;
        
        this.statusMessage = this.audioEnabled 
          ? 'Audio is now ON' 
          : 'Audio is now MUTED';
      }
    }
  }

  stopVideo(): void {
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => {
        track.stop();
      });
      this.mediaStream = null;
    }

    if (this.videoElement?.nativeElement) {
      this.videoElement.nativeElement.srcObject = null;
    }

    this.isActive = false;
    this.showVideo = false;
    this.videoEnabled = true;
    this.audioEnabled = true;
    
    this.statusTitle = 'Camera Stopped';
    this.statusMessage = 'Camera and microphone have been turned off. Click "Start Camera" to test again.';
    this.statusClass = 'ready';

    console.log('Video stopped');
  }
}
