import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-simple-video-call',
  template: `
    <div class="video-call-container">
      <div class="header">
        <h2>🎥 Simple Video Call</h2>
        <p>Room: {{ roomId }}</p>
      </div>

      <div class="status" [ngClass]="statusClass">
        {{ statusMessage }}
      </div>

      <div class="video-section">
        <div class="video-grid">
          <div class="video-item">
            <h4>Your Video</h4>
            <video #localVideo autoplay muted playsinline></video>
          </div>
          <div class="video-item">
            <h4>Remote Video</h4>
            <video #remoteVideo autoplay playsinline></video>
            <div *ngIf="!remoteConnected" class="waiting">
              Waiting for other participant...
            </div>
          </div>
        </div>
      </div>

      <div class="controls">
        <button 
          class="btn btn-success" 
          (click)="startCall()"
          [disabled]="isConnected">
          {{ isConnected ? 'Connected' : 'Start Call' }}
        </button>
        
        <button 
          class="btn btn-warning" 
          (click)="toggleVideo()"
          [disabled]="!isConnected">
          {{ videoEnabled ? 'Turn Off Video' : 'Turn On Video' }}
        </button>
        
        <button 
          class="btn btn-warning" 
          (click)="toggleAudio()"
          [disabled]="!isConnected">
          {{ audioEnabled ? 'Mute' : 'Unmute' }}
        </button>
        
        <button 
          class="btn btn-danger" 
          (click)="endCall()">
          End Call
        </button>
      </div>

      <div class="instructions">
        <h5>How to test:</h5>
        <ol>
          <li>Click "Start Call" to test your camera and microphone</li>
          <li>You should see your own video feed</li>
          <li>Use the controls to toggle video/audio on and off</li>
          <li>This demonstrates the basic video calling functionality</li>
        </ol>
        <p><strong>Note:</strong> This is a local test mode. For full peer-to-peer calls, a signaling server would be needed.</p>
      </div>
    </div>
  `,
  styles: [`
    .video-call-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 5px;
      text-align: center;
      font-weight: bold;
    }
    
    .status.connecting { background: #fff3cd; color: #856404; }
    .status.connected { background: #d4edda; color: #155724; }
    .status.error { background: #f8d7da; color: #721c24; }
    .status.ready { background: #d1ecf1; color: #0c5460; }
    
    .video-section {
      margin: 20px 0;
    }
    
    .video-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
    
    .video-item {
      text-align: center;
    }
    
    .video-item h4 {
      margin-bottom: 10px;
    }
    
    video {
      width: 100%;
      max-width: 400px;
      height: 300px;
      background: #000;
      border-radius: 8px;
    }
    
    .waiting {
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      margin-top: 10px;
      color: #6c757d;
    }
    
    .controls {
      text-align: center;
      margin: 20px 0;
    }
    
    .controls button {
      margin: 5px;
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: black; }
    .btn-danger { background: #dc3545; color: white; }
    .btn:disabled { opacity: 0.6; cursor: not-allowed; }
    
    .instructions {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      margin-top: 20px;
    }
    
    @media (max-width: 768px) {
      .video-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class SimpleVideoCallComponent implements OnInit, OnDestroy {
  @ViewChild('localVideo', { static: false }) localVideo!: ElementRef<HTMLVideoElement>;
  @ViewChild('remoteVideo', { static: false }) remoteVideo!: ElementRef<HTMLVideoElement>;

  roomId: string = '';
  isConnected = false;
  remoteConnected = false;
  videoEnabled = true;
  audioEnabled = true;
  statusMessage = 'Ready to start video call';
  statusClass = 'ready';

  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private peerConnection: RTCPeerConnection | null = null;
  private socket: WebSocket | null = null;

  constructor(private route: ActivatedRoute) {
    this.roomId = this.route.snapshot.params['roomId'] || 'default-room';
  }

  ngOnInit(): void {
    this.setupWebSocket();
  }

  ngOnDestroy(): void {
    this.cleanup();
  }

  private setupWebSocket(): void {
    // For now, we'll work in local mode without a signaling server
    // This allows testing the basic video functionality
    console.log('Running in local mode - perfect for testing your own video');
    this.statusMessage = 'Ready to start video call (local mode)';
    this.statusClass = 'ready';
  }

  async startCall(): Promise<void> {
    try {
      this.statusMessage = 'Starting video call...';
      this.statusClass = 'connecting';

      // Get user media
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });

      // Display local video
      this.localVideo.nativeElement.srcObject = this.localStream;

      this.isConnected = true;
      this.statusMessage = '✅ Video call started! Your camera and microphone are working.';
      this.statusClass = 'connected';

      // For demo purposes, show a message about remote connection
      setTimeout(() => {
        this.statusMessage = '📹 Local video test successful! Camera and microphone are working properly.';
      }, 2000);

    } catch (error) {
      console.error('Error starting call:', error);
      this.statusMessage = 'Failed to start call. Please allow camera and microphone access.';
      this.statusClass = 'error';
    }
  }

  // Peer connection setup removed for local testing mode
  // In a full implementation, this would handle WebRTC peer connections

  // Signaling message handling removed for local testing mode
  // In a full implementation, this would handle WebRTC signaling

  toggleVideo(): void {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        this.videoEnabled = videoTrack.enabled;
      }
    }
  }

  toggleAudio(): void {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        this.audioEnabled = audioTrack.enabled;
      }
    }
  }

  endCall(): void {
    this.cleanup();
    this.isConnected = false;
    this.remoteConnected = false;
    this.statusMessage = 'Call ended';
    this.statusClass = 'ready';
  }

  private cleanup(): void {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    if (this.localVideo?.nativeElement) {
      this.localVideo.nativeElement.srcObject = null;
    }

    if (this.remoteVideo?.nativeElement) {
      this.remoteVideo.nativeElement.srcObject = null;
    }
  }
}
