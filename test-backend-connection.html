<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Backend Connection Test</h1>
        <p>This page tests the connection between the Angular frontend and Spring Boot backend.</p>

        <div class="test-section info">
            <h3>📋 Test Configuration</h3>
            <ul>
                <li><strong>Frontend:</strong> http://localhost:4200</li>
                <li><strong>Backend:</strong> http://localhost:8081</li>
                <li><strong>Prescription Analyzer:</strong> Direct Gemini API (bypasses backend)</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🏥 Health Check Test</h3>
            <button onclick="testHealthCheck()">Test Backend Health</button>
            <div id="healthResult"></div>
        </div>

        <div class="test-section">
            <h3>👥 User Registration Test</h3>
            <button onclick="testUserRegistration()">Test User Registration</button>
            <div id="registrationResult"></div>
        </div>

        <div class="test-section">
            <h3>🔐 Login Test</h3>
            <button onclick="testLogin()">Test Login</button>
            <div id="loginResult"></div>
        </div>

        <div class="test-section">
            <h3>📅 Appointments Test</h3>
            <button onclick="testAppointments()">Test Appointments API</button>
            <div id="appointmentsResult"></div>
        </div>

        <div class="test-section">
            <h3>💊 Prescription Analyzer Test</h3>
            <button onclick="testPrescriptionAnalyzer()">Test Gemini API (Direct)</button>
            <div id="prescriptionResult"></div>
        </div>
    </div>

    <script>
        async function testHealthCheck() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.innerHTML = '<p>Testing backend health...</p>';
            
            try {
                const response = await fetch('http://localhost:8081/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Backend Health Check Successful</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Backend Health Check Failed</h4>
                        <p>Error: ${error.message}</p>
                        <p>Make sure the Spring Boot backend is running on port 8081</p>
                    </div>
                `;
            }
        }

        async function testUserRegistration() {
            const resultDiv = document.getElementById('registrationResult');
            resultDiv.innerHTML = '<p>Testing user registration...</p>';
            
            const testUser = {
                fullName: 'Test User',
                email: `test.user.${Date.now()}@example.com`,
                password: 'TestPassword123!',
                role: 'PATIENT'
            };
            
            try {
                const response = await fetch('http://localhost:8081/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testUser)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ User Registration Successful</h4>
                            <p>User: ${data.fullName} (${data.email})</p>
                            <p>Role: ${data.role}</p>
                            <p>Token received: ${data.token ? 'Yes' : 'No'}</p>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.message || response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ User Registration Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<p>Testing login with test credentials...</p>';
            
            const loginData = {
                email: '<EMAIL>',
                password: 'password123'
            };
            
            try {
                const response = await fetch('http://localhost:8081/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Store token for subsequent tests
                    window.testToken = data.token;
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Login Successful</h4>
                            <p>User: ${data.fullName} (${data.email})</p>
                            <p>Role: ${data.role}</p>
                            <p>Token stored for subsequent tests</p>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.message || response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Login Failed</h4>
                        <p>Error: ${error.message}</p>
                        <p>Make sure test user exists in the database</p>
                    </div>
                `;
            }
        }

        async function testAppointments() {
            const resultDiv = document.getElementById('appointmentsResult');
            resultDiv.innerHTML = '<p>Testing appointments API...</p>';
            
            if (!window.testToken) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ No Authentication Token</h4>
                        <p>Please run the login test first to get an authentication token</p>
                    </div>
                `;
                return;
            }
            
            try {
                const response = await fetch('http://localhost:8081/api/appointments', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${window.testToken}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Appointments API Successful</h4>
                            <p>Found ${data.length} appointments</p>
                            <pre>${JSON.stringify(data.slice(0, 2), null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Appointments API Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testPrescriptionAnalyzer() {
            const resultDiv = document.getElementById('prescriptionResult');
            resultDiv.innerHTML = '<p>Testing Gemini API (this should work independently)...</p>';
            
            // Simple test with a small base64 image
            const testImageBase64 = '/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=';
            
            try {
                const response = await fetch('https://us-central1-said-eb2f5.cloudfunctions.net/gemini_medical_assistant', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image_base64: testImageBase64
                    })
                });
                
                const data = await response.text();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Gemini API Test Successful</h4>
                            <p>API is accessible and responding</p>
                            <p><strong>Note:</strong> This API bypasses the backend and calls directly from frontend</p>
                            <details>
                                <summary>View Response</summary>
                                <pre>${data.substring(0, 500)}...</pre>
                            </details>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Gemini API Test Failed</h4>
                        <p>Error: ${error.message}</p>
                        <p>This might be due to CORS restrictions in the browser</p>
                    </div>
                `;
            }
        }

        // Auto-run health check on page load
        window.onload = function() {
            testHealthCheck();
        };
    </script>
</body>
</html>
