import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { VideoNotificationService, VideoNotification } from '../../../core/services/video-notification.service';

@Component({
  selector: 'app-video-notifications',
  template: `
    <div class="notifications-container">
      <!-- Notification Bell Icon -->
      <div class="notification-bell" (click)="toggleNotifications()">
        <i class="fas fa-bell"></i>
        <span class="badge" *ngIf="unreadCount > 0">{{ unreadCount }}</span>
      </div>

      <!-- Notifications Dropdown -->
      <div class="notifications-dropdown" *ngIf="showNotifications">
        <div class="dropdown-header">
          <h4>📹 Video Notifications</h4>
          <div class="header-actions">
            <button class="btn-link" (click)="markAllAsRead()" *ngIf="unreadCount > 0">
              Mark all read
            </button>
            <button class="btn-close" (click)="toggleNotifications()">×</button>
          </div>
        </div>

        <div class="notifications-list">
          <div *ngIf="notifications.length === 0" class="no-notifications">
            <i class="fas fa-bell-slash"></i>
            <p>No video notifications</p>
          </div>

          <div 
            *ngFor="let notification of notifications" 
            class="notification-item"
            [ngClass]="{ 'unread': !notification.read, 'urgent': notification.urgent }"
            (click)="handleNotificationClick(notification)">
            
            <div class="notification-icon">
              <i [ngClass]="getNotificationIcon(notification.type)"></i>
            </div>
            
            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.message }}</div>
              <div class="notification-time">{{ getTimeAgo(notification.timestamp) }}</div>
            </div>
            
            <div class="notification-actions">
              <button 
                class="btn-action join" 
                *ngIf="isJoinableNotification(notification)"
                (click)="joinConsultation(notification, $event)">
                Join
              </button>
              <button 
                class="btn-action dismiss" 
                (click)="dismissNotification(notification, $event)">
                ×
              </button>
            </div>
          </div>
        </div>

        <div class="dropdown-footer" *ngIf="notifications.length > 3">
          <button class="btn-link" (click)="viewAllNotifications()">
            View all notifications
          </button>
        </div>
      </div>

      <!-- Overlay -->
      <div class="overlay" *ngIf="showNotifications" (click)="toggleNotifications()"></div>
    </div>

    <!-- Urgent Notification Toast -->
    <div class="urgent-toast" *ngFor="let urgent of urgentNotifications" 
         [ngClass]="'toast-' + urgent.type.toLowerCase()">
      <div class="toast-content">
        <div class="toast-icon">
          <i [ngClass]="getNotificationIcon(urgent.type)"></i>
        </div>
        <div class="toast-text">
          <div class="toast-title">{{ urgent.title }}</div>
          <div class="toast-message">{{ urgent.message }}</div>
        </div>
        <div class="toast-actions">
          <button class="btn-toast join" (click)="joinConsultation(urgent)">Join Now</button>
          <button class="btn-toast dismiss" (click)="dismissUrgentNotification(urgent)">×</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .notifications-container {
      position: relative;
      display: inline-block;
    }
    
    .notification-bell {
      position: relative;
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      transition: background-color 0.3s ease;
    }
    
    .notification-bell:hover {
      background-color: rgba(0,0,0,0.1);
    }
    
    .notification-bell i {
      font-size: 20px;
      color: #6c757d;
    }
    
    .badge {
      position: absolute;
      top: 0;
      right: 0;
      background: #dc3545;
      color: white;
      border-radius: 50%;
      padding: 2px 6px;
      font-size: 12px;
      font-weight: bold;
      min-width: 18px;
      text-align: center;
    }
    
    .notifications-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      width: 400px;
      max-height: 500px;
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      z-index: 1000;
      overflow: hidden;
    }
    
    .dropdown-header {
      padding: 15px;
      background: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .dropdown-header h4 {
      margin: 0;
      font-size: 16px;
      color: #2c3e50;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }
    
    .btn-link {
      background: none;
      border: none;
      color: #007bff;
      cursor: pointer;
      font-size: 12px;
      text-decoration: underline;
    }
    
    .btn-close {
      background: none;
      border: none;
      font-size: 18px;
      cursor: pointer;
      color: #6c757d;
    }
    
    .notifications-list {
      max-height: 350px;
      overflow-y: auto;
    }
    
    .no-notifications {
      padding: 40px;
      text-align: center;
      color: #6c757d;
    }
    
    .no-notifications i {
      font-size: 24px;
      margin-bottom: 10px;
    }
    
    .notification-item {
      display: flex;
      padding: 15px;
      border-bottom: 1px solid #f1f3f4;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }
    
    .notification-item:hover {
      background-color: #f8f9fa;
    }
    
    .notification-item.unread {
      background-color: #e3f2fd;
      border-left: 3px solid #2196f3;
    }
    
    .notification-item.urgent {
      border-left: 3px solid #f44336;
      background-color: #ffebee;
    }
    
    .notification-icon {
      margin-right: 12px;
      font-size: 18px;
      color: #007bff;
      width: 24px;
      text-align: center;
    }
    
    .notification-content {
      flex: 1;
    }
    
    .notification-title {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 4px;
      font-size: 14px;
    }
    
    .notification-message {
      color: #6c757d;
      font-size: 13px;
      line-height: 1.4;
      margin-bottom: 4px;
    }
    
    .notification-time {
      color: #adb5bd;
      font-size: 11px;
    }
    
    .notification-actions {
      display: flex;
      flex-direction: column;
      gap: 5px;
      margin-left: 10px;
    }
    
    .btn-action {
      padding: 4px 8px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 11px;
      font-weight: 500;
    }
    
    .btn-action.join {
      background: #28a745;
      color: white;
    }
    
    .btn-action.dismiss {
      background: #6c757d;
      color: white;
    }
    
    .dropdown-footer {
      padding: 10px;
      text-align: center;
      border-top: 1px solid #dee2e6;
      background: #f8f9fa;
    }
    
    .overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999;
    }
    
    .urgent-toast {
      position: fixed;
      top: 20px;
      right: 20px;
      width: 350px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.2);
      z-index: 1001;
      animation: slideIn 0.3s ease-out;
    }
    
    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    .toast-content {
      display: flex;
      padding: 15px;
      align-items: center;
    }
    
    .toast-icon {
      margin-right: 12px;
      font-size: 20px;
      color: #f44336;
    }
    
    .toast-text {
      flex: 1;
    }
    
    .toast-title {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 4px;
    }
    
    .toast-message {
      color: #6c757d;
      font-size: 13px;
    }
    
    .toast-actions {
      display: flex;
      flex-direction: column;
      gap: 5px;
      margin-left: 10px;
    }
    
    .btn-toast {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
    }
    
    .btn-toast.join {
      background: #28a745;
      color: white;
    }
    
    .btn-toast.dismiss {
      background: #6c757d;
      color: white;
    }
  `]
})
export class VideoNotificationsComponent implements OnInit, OnDestroy {
  notifications: VideoNotification[] = [];
  urgentNotifications: VideoNotification[] = [];
  unreadCount = 0;
  showNotifications = false;
  
  private subscriptions: Subscription[] = [];

  constructor(
    private videoNotificationService: VideoNotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Request notification permission
    this.videoNotificationService.requestNotificationPermission();

    // Subscribe to notifications
    this.subscriptions.push(
      this.videoNotificationService.notifications$.subscribe(notifications => {
        this.notifications = notifications.slice(0, 5); // Show only latest 5
        this.updateUrgentNotifications();
      })
    );

    // Subscribe to unread count
    this.subscriptions.push(
      this.videoNotificationService.unreadCount$.subscribe(count => {
        this.unreadCount = count;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private updateUrgentNotifications(): void {
    this.urgentNotifications = this.videoNotificationService.getUrgentNotifications();
  }

  toggleNotifications(): void {
    this.showNotifications = !this.showNotifications;
  }

  handleNotificationClick(notification: VideoNotification): void {
    this.videoNotificationService.markAsRead(notification.id);
    
    if (this.isJoinableNotification(notification)) {
      this.joinConsultation(notification);
    }
  }

  joinConsultation(notification: VideoNotification, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    
    this.videoNotificationService.markAsRead(notification.id);
    const consultationLink = this.videoNotificationService.getConsultationLink(notification.consultationId);
    this.router.navigate([consultationLink]);
    this.showNotifications = false;
  }

  dismissNotification(notification: VideoNotification, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    
    this.videoNotificationService.deleteNotification(notification.id);
  }

  dismissUrgentNotification(notification: VideoNotification): void {
    this.videoNotificationService.markAsRead(notification.id);
    this.updateUrgentNotifications();
  }

  markAllAsRead(): void {
    this.videoNotificationService.markAllAsRead();
  }

  viewAllNotifications(): void {
    this.router.navigate(['/notifications']);
    this.showNotifications = false;
  }

  isJoinableNotification(notification: VideoNotification): boolean {
    return ['CONSULTATION_READY', 'DOCTOR_JOINED', 'PATIENT_JOINED'].includes(notification.type);
  }

  getNotificationIcon(type: string): string {
    switch (type) {
      case 'APPOINTMENT_REMINDER':
        return 'fas fa-clock';
      case 'DOCTOR_JOINED':
        return 'fas fa-user-md';
      case 'PATIENT_JOINED':
        return 'fas fa-user';
      case 'CONSULTATION_READY':
        return 'fas fa-video';
      default:
        return 'fas fa-bell';
    }
  }

  getTimeAgo(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - new Date(timestamp).getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  }
}
