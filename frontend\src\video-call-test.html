<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HealthConnect Video Call Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .video-section {
            flex: 1;
            text-align: center;
        }
        video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background: #000;
            border-radius: 8px;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            margin: 5px;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 HealthConnect Video Call Test</h1>
        <p>This page tests the Agora video calling functionality independently.</p>
        
        <div class="status info" id="status">
            Ready to test video calling...
        </div>
        
        <div class="controls">
            <button class="btn-primary" onclick="testAgoraConfig()">Test Agora Config</button>
            <button class="btn-success" onclick="initializeVideo()">Initialize Video</button>
            <button class="btn-warning" onclick="joinChannel()">Join Channel</button>
            <button class="btn-danger" onclick="leaveChannel()">Leave Channel</button>
        </div>
        
        <div class="video-container">
            <div class="video-section">
                <h3>Local Video</h3>
                <video id="localVideo" autoplay muted playsinline></video>
            </div>
            <div class="video-section">
                <h3>Remote Video</h3>
                <video id="remoteVideo" autoplay playsinline></video>
            </div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script src="https://download.agora.io/sdk/release/AgoraRTC_N-4.23.4.js"></script>
    <script>
        // Configuration
        const APP_ID = 'e4e46730b7c246babef60cdf947704e3';
        const BACKEND_URL = 'http://localhost:8081/api';
        const CHANNEL_NAME = 'test-room-' + Date.now();
        const UID = Math.floor(Math.random() * 10000);
        
        let client = null;
        let localVideoTrack = null;
        let localAudioTrack = null;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
            
            if (type === 'error') {
                updateStatus(message, 'error');
            } else if (type === 'success') {
                updateStatus(message, 'success');
            }
        }
        
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        async function testAgoraConfig() {
            try {
                log('🔧 Testing Agora configuration...');
                
                // Test backend token endpoint
                const response = await fetch(`${BACKEND_URL}/agora/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        channelName: CHANNEL_NAME,
                        uid: UID
                    })
                });
                
                if (response.ok) {
                    const tokenData = await response.json();
                    log(`✅ Backend token service working: ${tokenData.status}`, 'success');
                    log(`📋 App ID: ${tokenData.appId}`);
                    log(`🔑 Token: ${tokenData.token ? 'Generated' : 'Demo mode'}`);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                // Test Agora SDK
                if (typeof AgoraRTC !== 'undefined') {
                    log('✅ Agora SDK loaded successfully', 'success');
                } else {
                    throw new Error('Agora SDK not loaded');
                }
                
            } catch (error) {
                log(`❌ Configuration test failed: ${error.message}`, 'error');
            }
        }
        
        async function initializeVideo() {
            try {
                log('🎥 Initializing video...');
                
                // Create Agora client
                client = AgoraRTC.createClient({ mode: 'rtc', codec: 'vp8' });
                log('✅ Agora client created');
                
                // Get user media
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480 },
                    audio: true
                });
                
                const localVideo = document.getElementById('localVideo');
                localVideo.srcObject = stream;
                
                log('✅ Local video initialized', 'success');
                updateStatus('Video initialized successfully', 'success');
                
            } catch (error) {
                log(`❌ Video initialization failed: ${error.message}`, 'error');
            }
        }
        
        async function joinChannel() {
            try {
                if (!client) {
                    throw new Error('Client not initialized. Please initialize video first.');
                }
                
                log('🔗 Joining Agora channel...');
                
                // Get token from backend
                const response = await fetch(`${BACKEND_URL}/agora/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        channelName: CHANNEL_NAME,
                        uid: UID
                    })
                });
                
                const tokenData = await response.json();
                const token = tokenData.token === 'null' || !tokenData.token ? null : tokenData.token;
                
                // Join channel
                await client.join(APP_ID, CHANNEL_NAME, token, UID);
                log(`✅ Joined channel: ${CHANNEL_NAME} with UID: ${UID}`, 'success');
                
                // Create and publish local tracks
                localVideoTrack = await AgoraRTC.createCameraVideoTrack();
                localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
                
                // Play local video
                localVideoTrack.play('localVideo');
                
                // Publish tracks
                await client.publish([localVideoTrack, localAudioTrack]);
                log('✅ Local tracks published', 'success');
                
                updateStatus('Successfully joined video channel', 'success');
                
            } catch (error) {
                log(`❌ Failed to join channel: ${error.message}`, 'error');
            }
        }
        
        async function leaveChannel() {
            try {
                if (localVideoTrack) {
                    localVideoTrack.stop();
                    localVideoTrack.close();
                    localVideoTrack = null;
                }
                
                if (localAudioTrack) {
                    localAudioTrack.stop();
                    localAudioTrack.close();
                    localAudioTrack = null;
                }
                
                if (client) {
                    await client.leave();
                    log('✅ Left channel successfully', 'success');
                }
                
                updateStatus('Left video channel', 'info');
                
            } catch (error) {
                log(`❌ Error leaving channel: ${error.message}`, 'error');
            }
        }
        
        // Initialize on page load
        window.addEventListener('load', () => {
            log('🚀 Video call test page loaded');
            log(`📋 Channel: ${CHANNEL_NAME}`);
            log(`👤 UID: ${UID}`);
        });
    </script>
</body>
</html>
