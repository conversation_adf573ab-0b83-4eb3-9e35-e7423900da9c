import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule } from '@angular/common/http';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { VideoCallTestComponent } from './video-call-test/video-call-test.component';
import { SimpleVideoCallComponent } from './simple-video-call/simple-video-call.component';
import { BasicVideoTestComponent } from './basic-video-test/basic-video-test.component';
import { PatientDoctorVideoComponent } from './patient-doctor-video/patient-doctor-video.component';
import { VideoNotificationsComponent } from './shared/components/video-notifications/video-notifications.component';

// Core modules
import { CoreModule } from './core/core.module';
import { SharedModule } from './shared/shared.module';

@NgModule({
  declarations: [
    AppComponent,
    VideoCallTestComponent,
    SimpleVideoCallComponent,
    BasicVideoTestComponent,
    PatientDoctorVideoComponent,
    VideoNotificationsComponent
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    ReactiveFormsModule,
    FormsModule,
    AppRoutingModule,
    CoreModule,
    SharedModule
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
