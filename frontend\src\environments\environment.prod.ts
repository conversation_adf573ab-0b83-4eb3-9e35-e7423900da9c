export const environment = {
  production: true,
  apiUrl: 'https://healthconnect-backend-1026546995867.us-central1.run.app/api',
  wsUrl: 'https://healthconnect-backend-1026546995867.us-central1.run.app/ws',
  appName: 'HealthConnect',
  version: '1.0.0',
  agora: {
    appId: 'e4e46730b7c246babef60cdf947704e3'
  },
  geminiApiUrl: 'https://us-central1-said-eb2f5.cloudfunctions.net/gemini_medical_assistant',
  websocket: {
    url: 'https://healthconnect-backend-1026546995867.us-central1.run.app/ws',
    maxReconnectAttempts: 5,
    reconnectInterval: 3000,
    heartbeatIncoming: 25000,
    heartbeatOutgoing: 25000
  }
};
