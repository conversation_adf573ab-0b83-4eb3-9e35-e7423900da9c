<!DOCTYPE html>
<html>
<head>
    <title>Test Prescription Analyzer API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .upload-area { 
            border: 2px dashed #ccc; 
            padding: 20px; 
            text-align: center; 
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover { border-color: #007bff; }
        .result { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 20px 0;
            white-space: pre-wrap;
        }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        .loading { background: #fff3cd; color: #856404; }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer;
        }
        button:disabled { background: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Prescription Analyzer Test</h1>
        <p>Test the Gemini Medical Assistant API integration</p>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileSelect(event)">
            <p>📁 Click here to select a prescription image</p>
            <p><small>Supports: JPEG, PNG, GIF, WebP</small></p>
        </div>
        
        <div id="fileInfo" style="display: none;">
            <p><strong>Selected file:</strong> <span id="fileName"></span></p>
            <p><strong>Size:</strong> <span id="fileSize"></span></p>
            <button onclick="analyzePrescription()" id="analyzeBtn">🔍 Analyze Prescription</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        let selectedFile = null;

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                selectedFile = file;
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = formatFileSize(file.size);
                document.getElementById('fileInfo').style.display = 'block';
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        async function analyzePrescription() {
            if (!selectedFile) {
                showResult('Please select a file first', 'error');
                return;
            }

            const analyzeBtn = document.getElementById('analyzeBtn');
            analyzeBtn.disabled = true;
            analyzeBtn.textContent = '🔄 Analyzing...';
            
            showResult('Converting image to base64...', 'loading');

            try {
                // Convert to base64
                const base64 = await fileToBase64(selectedFile);
                
                showResult('Calling Gemini Medical Assistant API...', 'loading');

                // Call the API
                const response = await fetch('https://us-central1-said-eb2f5.cloudfunctions.net/gemini_medical_assistant', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image_base64: base64
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.text();
                showResult('✅ Analysis Complete!\n\n' + result, 'success');

            } catch (error) {
                console.error('Error:', error);
                showResult('❌ Error: ' + error.message, 'error');
            } finally {
                analyzeBtn.disabled = false;
                analyzeBtn.textContent = '🔍 Analyze Prescription';
            }
        }

        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    const base64 = reader.result.split(',')[1];
                    resolve(base64);
                };
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + type;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
