import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, interval } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { AuthService } from './auth.service';

export interface VideoNotification {
  id: string;
  type: 'APPOINTMENT_REMINDER' | 'DOCTOR_JOINED' | 'PATIENT_JOINED' | 'CONSULTATION_READY';
  title: string;
  message: string;
  consultationId: string;
  timestamp: Date;
  read: boolean;
  urgent: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class VideoNotificationService {
  private apiUrl = 'http://localhost:8081/api';
  private notificationsSubject = new BehaviorSubject<VideoNotification[]>([]);
  private unreadCountSubject = new BehaviorSubject<number>(0);

  public notifications$ = this.notificationsSubject.asObservable();
  public unreadCount$ = this.unreadCountSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {
    this.startNotificationPolling();
    this.createDemoNotifications();
  }

  private startNotificationPolling(): void {
    // Poll for new notifications every 30 seconds
    interval(30000).subscribe(() => {
      this.checkForNewNotifications();
    });
  }

  private createDemoNotifications(): void {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) return;

    const demoNotifications: VideoNotification[] = [];

    if (currentUser.role === 'PATIENT') {
      demoNotifications.push({
        id: 'notif-1',
        type: 'APPOINTMENT_REMINDER',
        title: '🏥 Video Consultation Reminder',
        message: 'Your video consultation with Dr. Sarah Johnson is scheduled in 15 minutes.',
        consultationId: 'demo-consultation',
        timestamp: new Date(),
        read: false,
        urgent: true
      });

      demoNotifications.push({
        id: 'notif-2',
        type: 'CONSULTATION_READY',
        title: '📹 Consultation Ready',
        message: 'Your video consultation room is now available. Click to join.',
        consultationId: 'demo-consultation',
        timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
        read: false,
        urgent: false
      });
    } else if (currentUser.role === 'DOCTOR') {
      demoNotifications.push({
        id: 'notif-3',
        type: 'PATIENT_JOINED',
        title: '👤 Patient Waiting',
        message: 'John Smith has joined the video consultation and is waiting for you.',
        consultationId: 'demo-consultation',
        timestamp: new Date(),
        read: false,
        urgent: true
      });

      demoNotifications.push({
        id: 'notif-4',
        type: 'APPOINTMENT_REMINDER',
        title: '🏥 Upcoming Consultation',
        message: 'Video consultation with John Smith starts in 10 minutes.',
        consultationId: 'demo-consultation',
        timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
        read: false,
        urgent: false
      });
    }

    this.notificationsSubject.next(demoNotifications);
    this.updateUnreadCount();
  }

  private checkForNewNotifications(): void {
    // In a real implementation, this would call the backend API
    console.log('Checking for new video consultation notifications...');
    
    // Simulate receiving a new notification occasionally
    if (Math.random() < 0.1) { // 10% chance
      this.addNewNotification();
    }
  }

  private addNewNotification(): void {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) return;

    const currentNotifications = this.notificationsSubject.value;
    
    const newNotification: VideoNotification = {
      id: 'notif-' + Date.now(),
      type: 'CONSULTATION_READY',
      title: '📹 New Consultation Available',
      message: currentUser.role === 'PATIENT' 
        ? 'Your doctor is ready to start the video consultation.'
        : 'A patient is requesting a video consultation.',
      consultationId: 'demo-consultation',
      timestamp: new Date(),
      read: false,
      urgent: true
    };

    const updatedNotifications = [newNotification, ...currentNotifications];
    this.notificationsSubject.next(updatedNotifications);
    this.updateUnreadCount();

    // Show browser notification if permission granted
    this.showBrowserNotification(newNotification);
  }

  private showBrowserNotification(notification: VideoNotification): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/assets/icons/video-call.png',
        badge: '/assets/icons/badge.png'
      });
    }
  }

  public requestNotificationPermission(): void {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        console.log('Notification permission:', permission);
      });
    }
  }

  public markAsRead(notificationId: string): void {
    const currentNotifications = this.notificationsSubject.value;
    const updatedNotifications = currentNotifications.map(notification =>
      notification.id === notificationId
        ? { ...notification, read: true }
        : notification
    );
    
    this.notificationsSubject.next(updatedNotifications);
    this.updateUnreadCount();
  }

  public markAllAsRead(): void {
    const currentNotifications = this.notificationsSubject.value;
    const updatedNotifications = currentNotifications.map(notification => ({
      ...notification,
      read: true
    }));
    
    this.notificationsSubject.next(updatedNotifications);
    this.updateUnreadCount();
  }

  public deleteNotification(notificationId: string): void {
    const currentNotifications = this.notificationsSubject.value;
    const updatedNotifications = currentNotifications.filter(
      notification => notification.id !== notificationId
    );
    
    this.notificationsSubject.next(updatedNotifications);
    this.updateUnreadCount();
  }

  public getNotificationsByType(type: string): VideoNotification[] {
    return this.notificationsSubject.value.filter(notification => notification.type === type);
  }

  public getUrgentNotifications(): VideoNotification[] {
    return this.notificationsSubject.value.filter(notification => notification.urgent && !notification.read);
  }

  private updateUnreadCount(): void {
    const unreadCount = this.notificationsSubject.value.filter(notification => !notification.read).length;
    this.unreadCountSubject.next(unreadCount);
  }

  // Method to trigger notifications for specific events
  public notifyDoctorJoined(consultationId: string): void {
    const notification: VideoNotification = {
      id: 'doctor-joined-' + Date.now(),
      type: 'DOCTOR_JOINED',
      title: '👨‍⚕️ Doctor Joined',
      message: 'The doctor has joined your video consultation.',
      consultationId,
      timestamp: new Date(),
      read: false,
      urgent: true
    };

    this.addNotificationToList(notification);
  }

  public notifyPatientJoined(consultationId: string): void {
    const notification: VideoNotification = {
      id: 'patient-joined-' + Date.now(),
      type: 'PATIENT_JOINED',
      title: '👤 Patient Joined',
      message: 'The patient has joined the video consultation.',
      consultationId,
      timestamp: new Date(),
      read: false,
      urgent: true
    };

    this.addNotificationToList(notification);
  }

  public notifyConsultationReady(consultationId: string, isPatient: boolean): void {
    const notification: VideoNotification = {
      id: 'consultation-ready-' + Date.now(),
      type: 'CONSULTATION_READY',
      title: '📹 Consultation Ready',
      message: isPatient 
        ? 'Your video consultation is ready to begin. Click to join.'
        : 'Patient consultation is ready. Click to join the video call.',
      consultationId,
      timestamp: new Date(),
      read: false,
      urgent: true
    };

    this.addNotificationToList(notification);
  }

  private addNotificationToList(notification: VideoNotification): void {
    const currentNotifications = this.notificationsSubject.value;
    const updatedNotifications = [notification, ...currentNotifications];
    this.notificationsSubject.next(updatedNotifications);
    this.updateUnreadCount();
    this.showBrowserNotification(notification);
  }

  // Get consultation link for notifications
  public getConsultationLink(consultationId: string): string {
    return `/video-consultation/${consultationId}`;
  }
}
