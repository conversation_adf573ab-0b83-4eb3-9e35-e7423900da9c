<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HealthConnect Video Consultation Fix & Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .step h3 {
            color: #007bff;
            margin-top: 0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .credentials {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .url-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 HealthConnect Video Consultation Fix</h1>
            <p>Complete solution to fix video consultation issues for both doctor and patient sides</p>
        </div>

        <div class="step">
            <h3>🔧 Step 1: Backend Health Check</h3>
            <p>First, let's verify the backend is running and accessible:</p>
            <button class="button" onclick="checkBackendHealth()">Check Backend Health</button>
            <div id="backend-status"></div>
        </div>

        <div class="step">
            <h3>👥 Step 2: Create Test Users & Login</h3>
            <p>Create test users and get authentication tokens:</p>
            <button class="button" onclick="createTestUsers()">Create Test Users</button>
            <button class="button" onclick="loginAsDoctor()">Login as Doctor</button>
            <button class="button" onclick="loginAsPatient()">Login as Patient</button>
            <div id="auth-status"></div>
            
            <div class="credentials">
                <strong>Test Credentials:</strong><br>
                <strong>Doctor:</strong> <EMAIL> / password123<br>
                <strong>Patient:</strong> <EMAIL> / password123
            </div>
        </div>

        <div class="step">
            <h3>📅 Step 3: Create Test Appointment</h3>
            <p>Create a video call appointment between doctor and patient:</p>
            <button class="button" onclick="createTestAppointment()">Create Video Appointment</button>
            <div id="appointment-status"></div>
        </div>

        <div class="step">
            <h3>🎥 Step 4: Create Video Consultation</h3>
            <p>Create the video consultation room from the appointment:</p>
            <button class="button" onclick="createVideoConsultation()">Create Video Consultation</button>
            <div id="consultation-status"></div>
        </div>

        <div class="step">
            <h3>🧪 Step 5: Test Agora Integration</h3>
            <p>Test the Agora video service and token generation:</p>
            <button class="button" onclick="testAgoraToken()">Test Agora Token</button>
            <button class="button" onclick="testAgoraSDK()">Test Agora SDK</button>
            <div id="agora-status"></div>
        </div>

        <div class="step">
            <h3>🚀 Step 6: Launch Video Consultation</h3>
            <p>Open the video consultation in both doctor and patient views:</p>
            <button class="button success" onclick="openDoctorView()">Open Doctor View</button>
            <button class="button success" onclick="openPatientView()">Open Patient View</button>
            <div id="launch-status"></div>
        </div>

        <div class="step">
            <h3>📋 Step 7: Direct URLs for Testing</h3>
            <p>Use these URLs to test specific components:</p>
            <div class="url-box">
                <strong>Frontend:</strong> <a href="http://localhost:4200" target="_blank">http://localhost:4200</a><br>
                <strong>Backend API:</strong> <a href="http://localhost:8081/api" target="_blank">http://localhost:8081/api</a><br>
                <strong>H2 Database:</strong> <a href="http://localhost:8081/h2-console" target="_blank">http://localhost:8081/h2-console</a><br>
                <strong>Telemedicine:</strong> <a href="http://localhost:4200/telemedicine/consultations" target="_blank">http://localhost:4200/telemedicine/consultations</a>
            </div>
        </div>

        <div class="log" id="log"></div>
    </div>

    <script>
        const BACKEND_URL = 'http://localhost:8081/api';
        const FRONTEND_URL = 'http://localhost:4200';
        
        let doctorToken = '';
        let patientToken = '';
        let appointmentId = '';
        let consultationId = '';
        let roomId = '';

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function checkBackendHealth() {
            try {
                log('🔍 Checking backend health...');
                const response = await fetch(`${BACKEND_URL}/health`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    log('✅ Backend is healthy and running on port 8081');
                    updateStatus('backend-status', '✅ Backend is running and accessible', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ Backend health check failed: ${error.message}`);
                updateStatus('backend-status', '❌ Backend is not accessible. Please start the backend first.', 'error');
            }
        }

        async function createTestUsers() {
            try {
                log('👥 Creating test users...');
                
                // Create doctor
                const doctorData = {
                    fullName: "Dr. John Smith",
                    email: "<EMAIL>",
                    password: "password123",
                    role: "DOCTOR",
                    specialization: "General Medicine",
                    licenseNumber: "DOC123456",
                    yearsOfExperience: 10,
                    consultationFee: 100.0
                };

                // Create patient
                const patientData = {
                    fullName: "Jane Doe",
                    email: "<EMAIL>",
                    password: "password123",
                    role: "PATIENT"
                };

                const doctorResponse = await fetch(`${BACKEND_URL}/auth/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(doctorData)
                });

                const patientResponse = await fetch(`${BACKEND_URL}/auth/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(patientData)
                });

                log('✅ Test users created successfully');
                updateStatus('auth-status', '✅ Test users created. You can now login.', 'success');
            } catch (error) {
                log(`❌ Error creating test users: ${error.message}`);
                updateStatus('auth-status', '❌ Failed to create test users. They might already exist.', 'error');
            }
        }

        async function loginAsDoctor() {
            try {
                log('🔐 Logging in as doctor...');
                const response = await fetch(`${BACKEND_URL}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: "<EMAIL>",
                        password: "password123"
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    doctorToken = data.token;
                    log('✅ Doctor login successful');
                    updateStatus('auth-status', '✅ Doctor authenticated successfully', 'success');
                } else {
                    throw new Error('Login failed');
                }
            } catch (error) {
                log(`❌ Doctor login failed: ${error.message}`);
                updateStatus('auth-status', '❌ Doctor login failed', 'error');
            }
        }

        async function loginAsPatient() {
            try {
                log('🔐 Logging in as patient...');
                const response = await fetch(`${BACKEND_URL}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: "<EMAIL>",
                        password: "password123"
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    patientToken = data.token;
                    log('✅ Patient login successful');
                    updateStatus('auth-status', '✅ Patient authenticated successfully', 'success');
                } else {
                    throw new Error('Login failed');
                }
            } catch (error) {
                log(`❌ Patient login failed: ${error.message}`);
                updateStatus('auth-status', '❌ Patient login failed', 'error');
            }
        }

        async function createTestAppointment() {
            if (!doctorToken || !patientToken) {
                updateStatus('appointment-status', '❌ Please login as both doctor and patient first', 'error');
                return;
            }

            try {
                log('📅 Creating test appointment...');
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                
                const appointmentData = {
                    doctorId: 1, // Assuming doctor has ID 1
                    patientId: 2, // Assuming patient has ID 2
                    date: tomorrow.toISOString().split('T')[0],
                    startTime: "14:00:00",
                    endTime: "14:30:00",
                    type: "VIDEO_CALL",
                    reasonForVisit: "General consultation",
                    status: "SCHEDULED"
                };

                const response = await fetch(`${BACKEND_URL}/appointments`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${patientToken}`
                    },
                    body: JSON.stringify(appointmentData)
                });

                if (response.ok) {
                    const appointment = await response.json();
                    appointmentId = appointment.id;
                    log(`✅ Test appointment created with ID: ${appointmentId}`);
                    updateStatus('appointment-status', `✅ Video appointment created (ID: ${appointmentId})`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ Error creating appointment: ${error.message}`);
                updateStatus('appointment-status', '❌ Failed to create appointment', 'error');
            }
        }

        async function createVideoConsultation() {
            if (!appointmentId) {
                updateStatus('consultation-status', '❌ Please create an appointment first', 'error');
                return;
            }

            try {
                log('🎥 Creating video consultation...');
                const response = await fetch(`${BACKEND_URL}/video-consultation/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${doctorToken}`
                    },
                    body: JSON.stringify({
                        appointmentId: appointmentId,
                        type: "ROUTINE_CHECKUP"
                    })
                });

                if (response.ok) {
                    const consultation = await response.json();
                    consultationId = consultation.id;
                    roomId = consultation.roomId;
                    log(`✅ Video consultation created - ID: ${consultationId}, Room: ${roomId}`);
                    updateStatus('consultation-status', `✅ Video consultation ready (Room: ${roomId})`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ Error creating video consultation: ${error.message}`);
                updateStatus('consultation-status', '❌ Failed to create video consultation', 'error');
            }
        }

        async function testAgoraToken() {
            try {
                log('🔑 Testing Agora token generation...');
                const response = await fetch(`${BACKEND_URL}/agora/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${doctorToken}`
                    },
                    body: JSON.stringify({
                        channelName: roomId || 'test-room',
                        uid: 12345
                    })
                });

                if (response.ok) {
                    const tokenData = await response.json();
                    log(`✅ Agora token generated successfully`);
                    log(`📋 Token: ${tokenData.token ? 'Valid' : 'Demo mode'}`);
                    updateStatus('agora-status', '✅ Agora token service working', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ Agora token test failed: ${error.message}`);
                updateStatus('agora-status', '❌ Agora token generation failed', 'error');
            }
        }

        async function testAgoraSDK() {
            log('🧪 Testing Agora SDK availability...');
            if (typeof AgoraRTC !== 'undefined') {
                log('✅ Agora SDK is loaded and available');
                updateStatus('agora-status', '✅ Agora SDK ready for video calls', 'success');
            } else {
                log('⚠️ Agora SDK not loaded (normal for this test page)');
                updateStatus('agora-status', '⚠️ Agora SDK will be loaded in the main app', 'info');
            }
        }

        function openDoctorView() {
            if (!consultationId) {
                updateStatus('launch-status', '❌ Please create a video consultation first', 'error');
                return;
            }

            const doctorUrl = `${FRONTEND_URL}/telemedicine/consultation/${consultationId}?role=doctor`;
            log(`🩺 Opening doctor view: ${doctorUrl}`);
            window.open(doctorUrl, '_blank');
            updateStatus('launch-status', '✅ Doctor view opened in new tab', 'success');
        }

        function openPatientView() {
            if (!consultationId) {
                updateStatus('launch-status', '❌ Please create a video consultation first', 'error');
                return;
            }

            const patientUrl = `${FRONTEND_URL}/telemedicine/consultation/${consultationId}?role=patient`;
            log(`🤒 Opening patient view: ${patientUrl}`);
            window.open(patientUrl, '_blank');
            updateStatus('launch-status', '✅ Patient view opened in new tab', 'success');
        }

        // Initialize
        log('🏥 HealthConnect Video Consultation Fix Tool initialized');
        log('📋 Follow the steps in order to fix and test video consultations');
    </script>
</body>
</html>
