# Google Cloud Build configuration for Frontend Only
steps:
  # Build Frontend Docker Image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/said-eb2f5/healthconnect-frontend:$BUILD_ID'
      - '-t'
      - 'gcr.io/said-eb2f5/healthconnect-frontend:latest'
      - './frontend'
    id: 'build-frontend'

  # Push Frontend Image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/said-eb2f5/healthconnect-frontend:$BUILD_ID'
    id: 'push-frontend'
    waitFor: ['build-frontend']

  # Deploy Frontend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'healthconnect-frontend'
      - '--image'
      - 'gcr.io/said-eb2f5/healthconnect-frontend:$BUILD_ID'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '80'
      - '--memory'
      - '512Mi'
      - '--cpu'
      - '1'
      - '--max-instances'
      - '5'
    id: 'deploy-frontend'
    waitFor: ['push-frontend']

# Store frontend image in Container Registry
images:
  - 'gcr.io/said-eb2f5/healthconnect-frontend:$BUILD_ID'
  - 'gcr.io/said-eb2f5/healthconnect-frontend:latest'

# Build options
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: 100
  logging: CLOUD_LOGGING_ONLY

# Build timeout
timeout: '1200s'
