#!/bin/bash

# HealthConnect Frontend Deployment Script
# Project ID: said-eb2f5

set -e

echo "🚀 Starting HealthConnect FRONTEND deployment to Google Cloud..."

# Configuration
PROJECT_ID="said-eb2f5"
REGION="us-central1"
FRONTEND_SERVICE="healthconnect-frontend"
BACKEND_SERVICE="healthconnect-backend"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    print_error "gcloud CLI is not installed. Please install it first."
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_warning "You are not authenticated with gcloud. Please run 'gcloud auth login'"
    exit 1
fi

# Set the project
print_status "Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Check if backend is deployed
print_status "Checking if backend is deployed..."
if ! gcloud run services describe $BACKEND_SERVICE --region=$REGION &> /dev/null; then
    print_error "Backend service not found. Please deploy backend first using ./deploy-backend.sh"
    exit 1
fi

# Get backend URL
print_status "Getting backend URL for frontend configuration..."
BACKEND_URL=$(gcloud run services describe $BACKEND_SERVICE --region=$REGION --format="value(status.url)")
print_status "Backend URL: $BACKEND_URL"

# Build and deploy frontend using Cloud Build
print_status "Starting Frontend deployment with Cloud Build..."
gcloud builds submit --config=deploy-frontend.yaml .

# Get frontend service URL
print_status "Getting frontend service URL..."
FRONTEND_URL=$(gcloud run services describe $FRONTEND_SERVICE --region=$REGION --format="value(status.url)")

# Update backend CORS configuration with frontend URL
print_status "Updating backend CORS configuration with frontend URL..."
gcloud run services update $BACKEND_SERVICE --region=$REGION --update-env-vars="CORS_ALLOWED_ORIGINS=$FRONTEND_URL,https://*.run.app"

print_success "Frontend deployment completed successfully!"
echo ""
echo "🌐 Service URLs:"
echo "   Frontend: $FRONTEND_URL"
echo "   Backend:  $BACKEND_URL"
echo ""
echo "📋 Test Credentials:"
echo "   Patient: <EMAIL> / password123"
echo "   Doctor:  <EMAIL> / password123"
echo ""
echo "🧪 Test Endpoints:"
echo "   Frontend Health: $FRONTEND_URL/health"
echo "   Backend Health:  $BACKEND_URL/actuator/health"
echo "   Backend API:     $BACKEND_URL/api/health"
echo ""
echo "🔧 To view logs:"
echo "   Frontend: gcloud run services logs read $FRONTEND_SERVICE --region=$REGION"
echo "   Backend:  gcloud run services logs read $BACKEND_SERVICE --region=$REGION"
echo ""
print_success "HealthConnect is now fully deployed on Google Cloud! 🎉"
