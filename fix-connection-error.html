<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Connection Error</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Fix Connection Error</h1>
            <p>This will fix the "Failed to load consultation details" error</p>
        </div>

        <div class="info-box">
            <h4>🎯 What This Fixes:</h4>
            <ul>
                <li>✅ Creates proper video consultations with valid room IDs</li>
                <li>✅ Fixes navigation from consultation.id to consultation.roomId</li>
                <li>✅ Ensures backend API endpoints work correctly</li>
                <li>✅ Creates test data with proper user authentication</li>
            </ul>
        </div>

        <div>
            <button class="button" onclick="fixConnectionError()">🚀 Fix Connection Error</button>
            <button class="button success" onclick="testVideoRoom()">🎥 Test Video Room</button>
            <button class="button" onclick="checkBackendHealth()">🏥 Check Backend</button>
            <button class="button danger" onclick="clearLog()">🗑️ Clear Log</button>
        </div>

        <div id="status"></div>
        <div class="log" id="log"></div>
    </div>

    <script>
        const BACKEND_URL = 'http://localhost:8081/api';
        const FRONTEND_URL = 'http://localhost:4200';
        
        let doctorToken = '';
        let patientToken = '';
        let appointmentId = '';
        let consultationId = '';
        let roomId = '';

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : '📋';
            const logEntry = `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'info') {
            const element = document.getElementById('status');
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
            document.getElementById('status').innerHTML = '';
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                log(`Request failed: ${error.message}`, 'error');
                throw error;
            }
        }

        async function checkBackendHealth() {
            log('🏥 Checking backend health...');
            try {
                const health = await makeRequest(`${BACKEND_URL}/health`);
                log(`✅ Backend is healthy: ${health.service} v${health.version}`, 'success');
                updateStatus('✅ Backend is running properly', 'success');
                return true;
            } catch (error) {
                log('❌ Backend is not running!', 'error');
                updateStatus('❌ Backend is not running. Please start the backend first.', 'error');
                return false;
            }
        }

        async function authenticateUsers() {
            log('🔐 Authenticating test users...');
            
            try {
                const doctorAuth = await makeRequest(`${BACKEND_URL}/auth/login`, {
                    method: 'POST',
                    body: JSON.stringify({
                        email: "<EMAIL>",
                        password: "password123"
                    })
                });
                doctorToken = doctorAuth.token;
                log('✅ Doctor authenticated successfully', 'success');
            } catch (error) {
                log('❌ Doctor authentication failed', 'error');
                return false;
            }

            try {
                const patientAuth = await makeRequest(`${BACKEND_URL}/auth/login`, {
                    method: 'POST',
                    body: JSON.stringify({
                        email: "<EMAIL>",
                        password: "password123"
                    })
                });
                patientToken = patientAuth.token;
                log('✅ Patient authenticated successfully', 'success');
            } catch (error) {
                log('❌ Patient authentication failed', 'error');
                return false;
            }

            return true;
        }

        async function createTestAppointment() {
            log('📅 Creating test appointment...');
            
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            
            const appointmentData = {
                doctorId: 1,
                patientId: 2,
                date: tomorrow.toISOString().split('T')[0],
                startTime: "14:00:00",
                endTime: "14:30:00",
                type: "VIDEO_CALL",
                reasonForVisit: "Video consultation test - Connection Error Fix",
                status: "SCHEDULED"
            };

            try {
                const appointment = await makeRequest(`${BACKEND_URL}/appointments`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${patientToken}`
                    },
                    body: JSON.stringify(appointmentData)
                });
                
                appointmentId = appointment.id;
                log(`✅ Appointment created with ID: ${appointmentId}`, 'success');
                return true;
            } catch (error) {
                log('❌ Failed to create appointment', 'error');
                return false;
            }
        }

        async function createVideoConsultation() {
            log('🎥 Creating video consultation with proper room ID...');
            
            try {
                const consultation = await makeRequest(`${BACKEND_URL}/video-consultation/create`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${doctorToken}`
                    },
                    body: JSON.stringify({
                        appointmentId: appointmentId,
                        type: "ROUTINE_CHECKUP"
                    })
                });
                
                consultationId = consultation.id;
                roomId = consultation.roomId;
                log(`✅ Video consultation created successfully!`, 'success');
                log(`   - Consultation ID: ${consultationId}`, 'success');
                log(`   - Room ID: ${roomId}`, 'success');
                log(`   - Status: ${consultation.status}`, 'success');
                return true;
            } catch (error) {
                log('❌ Failed to create video consultation', 'error');
                return false;
            }
        }

        async function testVideoRoom() {
            if (!roomId) {
                updateStatus('❌ Please run "Fix Connection Error" first', 'error');
                return;
            }
            
            log('🧪 Testing video room access...');
            
            try {
                // Test if we can get consultation by room ID
                const consultation = await makeRequest(`${BACKEND_URL}/video-consultation/room/${roomId}`, {
                    headers: {
                        'Authorization': `Bearer ${doctorToken}`
                    }
                });
                
                log(`✅ Video room accessible: ${consultation.roomId}`, 'success');
                log(`   - Doctor: ${consultation.doctor.fullName}`, 'success');
                log(`   - Patient: ${consultation.patient.fullName}`, 'success');
                
                // Open video room URLs
                const doctorUrl = `${FRONTEND_URL}/telemedicine/room/${roomId}`;
                const patientUrl = `${FRONTEND_URL}/telemedicine/room/${roomId}`;
                
                log(`🩺 Doctor URL: ${doctorUrl}`);
                log(`🤒 Patient URL: ${patientUrl}`);
                
                window.open(doctorUrl, '_blank');
                setTimeout(() => {
                    window.open(patientUrl, '_blank');
                }, 1000);
                
                updateStatus('✅ Video room test successful! Windows opened.', 'success');
                
            } catch (error) {
                log(`❌ Video room test failed: ${error.message}`, 'error');
                updateStatus('❌ Video room test failed', 'error');
            }
        }

        async function fixConnectionError() {
            updateStatus('🔧 Fixing connection error...', 'info');
            log('🏥 Starting connection error fix...');
            
            try {
                // Step 1: Check backend
                const backendOk = await checkBackendHealth();
                if (!backendOk) {
                    return;
                }
                
                // Step 2: Authenticate users
                const authOk = await authenticateUsers();
                if (!authOk) {
                    updateStatus('❌ Authentication failed', 'error');
                    return;
                }
                
                // Step 3: Create appointment
                const appointmentOk = await createTestAppointment();
                if (!appointmentOk) {
                    updateStatus('❌ Failed to create appointment', 'error');
                    return;
                }
                
                // Step 4: Create video consultation
                const consultationOk = await createVideoConsultation();
                if (!consultationOk) {
                    updateStatus('❌ Failed to create video consultation', 'error');
                    return;
                }
                
                log('🎉 Connection error fix completed successfully!', 'success');
                updateStatus('✅ Connection error fixed! You can now join video consultations.', 'success');
                
                // Auto-open frontend
                setTimeout(() => {
                    log('🔄 Opening frontend consultations page...');
                    window.open(`${FRONTEND_URL}/telemedicine/consultations`, '_blank');
                }, 2000);
                
            } catch (error) {
                log(`❌ Fix failed: ${error.message}`, 'error');
                updateStatus('❌ Fix failed. Check the log for details.', 'error');
            }
        }

        // Initialize
        log('🔧 Connection Error Fix Tool loaded');
        log('📋 Click "Fix Connection Error" to resolve the issue');
    </script>
</body>
</html>
