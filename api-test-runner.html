<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HealthConnect API Test Runner</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #2196F3;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #1976D2;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #FF9800;
            color: white;
        }
        
        .btn-warning:hover {
            background: #F57C00;
            transform: translateY(-2px);
        }
        
        .status-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid #4CAF50;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .status-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .status-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .passed { color: #4CAF50; }
        .failed { color: #f44336; }
        .total { color: #2196F3; }
        .rate { color: #FF9800; }
        
        .log-container {
            background: #1e1e1e;
            color: #f0f0f0;
            border-radius: 10px;
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .log-success { color: #4CAF50; }
        .log-error { color: #f44336; }
        .log-info { color: #2196F3; }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .endpoint-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .endpoint-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        
        .endpoint-item.testing { border-left-color: #FF9800; }
        .endpoint-item.passed { border-left-color: #4CAF50; }
        .endpoint-item.failed { border-left-color: #f44336; }
        
        .endpoint-name { font-weight: 500; }
        .endpoint-status { 
            padding: 4px 8px; 
            border-radius: 4px; 
            font-size: 12px; 
            font-weight: bold;
        }
        
        .status-pending { background: #e0e0e0; color: #666; }
        .status-testing { background: #FFF3E0; color: #F57C00; }
        .status-passed { background: #E8F5E8; color: #2E7D32; }
        .status-failed { background: #FFEBEE; color: #C62828; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .testing { animation: pulse 1.5s infinite; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 HealthConnect API Test Runner</h1>
            <p>Comprehensive testing suite for HealthConnect backend APIs</p>
        </div>
        
        <div class="content">
            <div class="test-controls">
                <button class="btn btn-primary" onclick="runFullTestSuite()">
                    🚀 Run Full Test Suite
                </button>
                <button class="btn btn-secondary" onclick="testGeminiOnly()">
                    🧠 Test Gemini API Only
                </button>
                <button class="btn btn-warning" onclick="clearLogs()">
                    🗑️ Clear Logs
                </button>
            </div>
            
            <div class="status-panel">
                <h3>Test Results</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="status-grid">
                    <div class="status-card">
                        <div class="status-number passed" id="passed-count">0</div>
                        <div class="status-label">Passed</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number failed" id="failed-count">0</div>
                        <div class="status-label">Failed</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number total" id="total-count">0</div>
                        <div class="status-label">Total</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number rate" id="success-rate">0%</div>
                        <div class="status-label">Success Rate</div>
                    </div>
                </div>
            </div>
            
            <div class="endpoint-list">
                <h3>API Endpoints</h3>
                <div id="endpoint-status">
                    <div class="endpoint-item" data-test="health">
                        <span class="endpoint-name">GET /api/health</span>
                        <span class="endpoint-status status-pending">Pending</span>
                    </div>
                    <div class="endpoint-item" data-test="cors">
                        <span class="endpoint-name">OPTIONS /api/health (CORS)</span>
                        <span class="endpoint-status status-pending">Pending</span>
                    </div>
                    <div class="endpoint-item" data-test="gemini">
                        <span class="endpoint-name">POST /gemini_medical_assistant</span>
                        <span class="endpoint-status status-pending">Pending</span>
                    </div>
                    <div class="endpoint-item" data-test="register">
                        <span class="endpoint-name">POST /api/auth/register</span>
                        <span class="endpoint-status status-pending">Pending</span>
                    </div>
                    <div class="endpoint-item" data-test="login">
                        <span class="endpoint-name">POST /api/auth/login</span>
                        <span class="endpoint-status status-pending">Pending</span>
                    </div>
                    <div class="endpoint-item" data-test="user">
                        <span class="endpoint-name">GET /api/users/me</span>
                        <span class="endpoint-status status-pending">Pending</span>
                    </div>
                    <div class="endpoint-item" data-test="doctors">
                        <span class="endpoint-name">GET /api/users/doctors</span>
                        <span class="endpoint-status status-pending">Pending</span>
                    </div>
                    <div class="endpoint-item" data-test="ai">
                        <span class="endpoint-name">POST /api/ai-health-bot/chat</span>
                        <span class="endpoint-status status-pending">Pending</span>
                    </div>
                    <div class="endpoint-item" data-test="appointments">
                        <span class="endpoint-name">GET /api/appointments</span>
                        <span class="endpoint-status status-pending">Pending</span>
                    </div>
                    <div class="endpoint-item" data-test="create-appointment">
                        <span class="endpoint-name">POST /api/appointments</span>
                        <span class="endpoint-status status-pending">Pending</span>
                    </div>
                </div>
            </div>
            
            <div class="log-container" id="log-container">
                <div class="log-entry log-info">🏥 HealthConnect API Test Runner initialized</div>
                <div class="log-entry log-info">Click "Run Full Test Suite" to begin testing...</div>
            </div>
        </div>
    </div>

    <script src="test-api-comprehensive.js"></script>
    <script>
        let currentTestResults = { passed: 0, failed: 0, total: 0 };
        
        function updateUI(results) {
            document.getElementById('passed-count').textContent = results.passed;
            document.getElementById('failed-count').textContent = results.failed;
            document.getElementById('total-count').textContent = results.total;
            
            const successRate = results.total > 0 ? ((results.passed / results.total) * 100).toFixed(1) : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
            
            const progressPercent = results.total > 0 ? (results.total / 10) * 100 : 0;
            document.getElementById('progress-fill').style.width = progressPercent + '%';
        }
        
        function updateEndpointStatus(testName, status) {
            const endpoint = document.querySelector(`[data-test="${testName}"]`);
            if (endpoint) {
                const statusElement = endpoint.querySelector('.endpoint-status');
                endpoint.className = `endpoint-item ${status}`;
                
                switch(status) {
                    case 'testing':
                        statusElement.textContent = 'Testing...';
                        statusElement.className = 'endpoint-status status-testing';
                        break;
                    case 'passed':
                        statusElement.textContent = 'Passed';
                        statusElement.className = 'endpoint-status status-passed';
                        break;
                    case 'failed':
                        statusElement.textContent = 'Failed';
                        statusElement.className = 'endpoint-status status-failed';
                        break;
                }
            }
        }
        
        function addLogEntry(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            logEntry.textContent = `[${timestamp}] ${icon} ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLogs() {
            const logContainer = document.getElementById('log-container');
            logContainer.innerHTML = '<div class="log-entry log-info">🗑️ Logs cleared</div>';
            
            // Reset endpoint statuses
            document.querySelectorAll('.endpoint-item').forEach(item => {
                item.className = 'endpoint-item';
                item.querySelector('.endpoint-status').textContent = 'Pending';
                item.querySelector('.endpoint-status').className = 'endpoint-status status-pending';
            });
            
            // Reset counters
            currentTestResults = { passed: 0, failed: 0, total: 0 };
            updateUI(currentTestResults);
        }
        
        async function runFullTestSuite() {
            addLogEntry('Starting comprehensive API test suite...', 'info');
            clearLogs();
            
            // Override the log function to update UI
            const originalConsoleLog = console.log;
            console.log = function(...args) {
                const message = args.join(' ');
                if (message.includes('✅')) {
                    addLogEntry(message.replace(/^\[.*?\] ✅ /, ''), 'success');
                } else if (message.includes('❌')) {
                    addLogEntry(message.replace(/^\[.*?\] ❌ /, ''), 'error');
                } else if (message.includes('ℹ️')) {
                    addLogEntry(message.replace(/^\[.*?\] ℹ️ /, ''), 'info');
                }
                originalConsoleLog.apply(console, args);
            };
            
            try {
                const results = await HealthConnectAPITest.runAllTests();
                currentTestResults = results;
                updateUI(results);
                
                addLogEntry(`Test suite completed: ${results.passed}/${results.total} passed`, 
                    results.failed === 0 ? 'success' : 'error');
                
            } catch (error) {
                addLogEntry(`Test suite failed: ${error.message}`, 'error');
            }
            
            // Restore original console.log
            console.log = originalConsoleLog;
        }
        
        async function testGeminiOnly() {
            addLogEntry('Testing Gemini Medical Assistant API only...', 'info');
            updateEndpointStatus('gemini', 'testing');
            
            try {
                const success = await HealthConnectAPITest.testGeminiMedicalAssistant();
                updateEndpointStatus('gemini', success ? 'passed' : 'failed');
                
                if (success) {
                    addLogEntry('Gemini Medical Assistant API is working correctly', 'success');
                } else {
                    addLogEntry('Gemini Medical Assistant API test failed', 'error');
                }
            } catch (error) {
                updateEndpointStatus('gemini', 'failed');
                addLogEntry(`Gemini test error: ${error.message}`, 'error');
            }
        }
        
        // Initialize
        addLogEntry('HealthConnect API Test Runner loaded successfully', 'success');
        addLogEntry('Backend URL: https://healthconnect-backend-1026546995867.us-central1.run.app', 'info');
        addLogEntry('Gemini API URL: https://us-central1-said-eb2f5.cloudfunctions.net/gemini_medical_assistant', 'info');
    </script>
</body>
</html>
