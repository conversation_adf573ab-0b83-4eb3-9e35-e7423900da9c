{"/api/gemini/*": {"target": "https://us-central1-said-eb2f5.cloudfunctions.net", "secure": true, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/gemini": "/gemini_medical_assistant"}, "headers": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS", "Access-Control-Allow-Headers": "Content-Type, Authorization"}}, "/api/*": {"target": "http://localhost:8081", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/ws/*": {"target": "http://localhost:8081", "secure": false, "changeOrigin": true, "ws": true, "logLevel": "debug"}}